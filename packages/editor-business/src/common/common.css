.tiptap.ProseMirror {
  width: 100%;
  height: 100%;
  min-height: inherit;
  outline: none;

  h1:first-child,
  h2:first-child,
  h3:first-child,
  h4:first-child {
    margin-top: 0;
  }

  p.is-editor-empty:first-child::before {
    color: hsl(var(--disabled));
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  .tableWrapper {
    padding: 0.5rem;
    overflow-x: auto;

    table {
      border-collapse: collapse;
      margin: 0;
      table-layout: fixed;
      width: 100%;

      .youmind-editor-node-paragraph-ui {
        margin: 0;
      }

      td,
      th {
        border: 1px solid hsl(var(--border));
        box-sizing: border-box;
        min-width: 1em;
        padding: 6px 8px;
        position: relative;
        vertical-align: top;

        > * {
          margin-bottom: 0;
        }
      }

      th {
        background-color: hsl(var(--muted));
        text-align: left;
        font-weight: normal;
      }

      .selectedCell:after {
        background: hsla(var(--accent));
        content: "";
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        pointer-events: none;
        position: absolute;
        z-index: 2;
      }

      .column-resize-handle {
        background-color: rgb(128, 195, 255);
        bottom: -2px;
        cursor: col-resize;
        position: absolute;
        right: -2px;
        top: 0;
        width: 2px;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          left: -8px;
          right: -8px;
          cursor: col-resize;
          z-index: 1;
        }
      }

      .grip-column {
        left: 0;
        top: -0.75rem;
        margin-left: -1px;
        height: 0.75rem;
        width: calc(100% + 1px);
        border-left-width: 1px;
        position: absolute;
        z-index: 10;
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.1);
      }

      .grip-row {
        position: absolute;
        z-index: 10;
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.05);
        left: -0.75rem;
        top: 0;
        margin-top: -1px;
        height: calc(100% + 1px);
        width: 0.75rem;
        border-top-width: 1px;
        border-color: rgba(0, 0, 0, 0.1);
      }

      .grip-column.last .grip-row.last {
        border-top-right-radius: 0.125rem;
      }

      .grip-column.selected,
      .grip-row.selected {
        border-color: transparent;
        background-color: rgba(0, 0, 0, 0.3);
        --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
        box-shadow:
          var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
          var(--tw-shadow);
      }

      .grip-row.selected {
        &::before {
          border-left-width: 2px;
          border-style: dotted;
          height: 0.625rem;
          margin-left: 2px;
          content: "";
        }
      }

      .grip-column.selected {
        &::before {
          border-top-width: 2px;
          margin-top: 2px;
          border-style: dotted;
          width: 0.625rem;
          content: "";
        }
      }

      tr {
        overflow: visible;
      }

      th {
        overflow: visible;
      }
    }
  }

  .youmind-editor-composing {
    .youmind-editor-slash-decoration {
      &[data-has-query="false"]::after {
        content: "" !important;
        color: rgba(55, 53, 47, 0.5);
        font-size: 14px !important;
        font-weight: 400 !important;
      }
    }
  }
}
