import { Bold, Highlight, Italic } from '@repo/editor-common';
import { Code, DiffChange, Link, Strike, Underline } from '../extension';
import type { EditorExtensionOptions } from './type';

// 编辑器 Mark 级节点
export const getMarkExtensionList = (options?: EditorExtensionOptions) => {
  return [
    Bold,
    Italic,
    Strike,
    Underline,
    Link.configure(options?.linkOptions),
    DiffChange,
    Highlight,
    Code,
  ];
};
