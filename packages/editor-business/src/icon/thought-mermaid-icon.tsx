interface Props extends React.SVGAttributes<SVGElement> {
  className?: string;
  size: number;
}
export function ThoughtMermaidIcon({ size, className, ...props }: Props) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <g clipPath="url(#clip0_4621_378)">
        <path
          d="M21.9907 4.00929C17.5678 3.81991 13.5064 6.54127 11.9997 10.704C10.4927 6.54127 6.43171 3.81991 2.00875 4.00929C1.93711 5.73746 2.30647 7.45543 3.08207 9.00144C3.85767 10.5475 5.01398 11.8706 6.44212 12.8464C7.17481 13.3501 7.77361 14.0249 8.18663 14.8123C8.59965 15.5997 8.81443 16.4759 8.81234 17.365V20.494H15.1879V17.365C15.1858 16.4759 15.4005 15.5996 15.8136 14.8122C16.2266 14.0248 16.8254 13.3501 17.5582 12.8464C18.9866 11.8709 20.143 10.5478 20.9186 9.00169C21.6942 7.4556 22.0629 5.73749 21.9907 4.00929Z"
          stroke="currentColor"
          strokeWidth="2.13534"
        />
      </g>
    </svg>
  );
}
