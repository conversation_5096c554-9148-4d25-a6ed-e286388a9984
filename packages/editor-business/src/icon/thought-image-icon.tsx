interface Props {
  className?: string;
  size: number;
}
export function ThoughtImageIcon({ size, className }: Props) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21 6.5C20.4477 6.5 20 6.94772 20 7.5V18C20 18.2652 19.8946 18.5195 19.707 18.707C19.5195 18.8946 19.2652 19 19 19H6C5.44772 19 5 19.4477 5 20C5 20.5523 5.44772 21 6 21H19C19.7956 21 20.5585 20.6837 21.1211 20.1211C21.6837 19.5585 22 18.7956 22 18V7.5C22 6.94772 21.5523 6.5 21 6.5ZM4 3C2.34315 3 1 4.34315 1 6V14C1 15.6569 2.34315 17 4 17H15C16.6569 17 18 15.6569 18 14V6C18 4.34315 16.6569 3 15 3H4ZM9.50195 13.3457C9.982 13.9629 10.8943 14.0196 11.4473 13.4668L13.5 11.4141L16 13.9141V14C16 14.5523 15.5523 15 15 15H4C3.44772 15 3 14.5523 3 14V13.9141L6.90625 10.0078L9.50195 13.3457ZM15 5C15.5523 5 16 5.44772 16 6V11.0859L14.9141 10C14.1818 9.26783 13.0228 9.2216 12.2373 9.8623L12.0859 10L10.5938 11.4912L8.48438 8.78027C7.79189 7.89014 6.515 7.75736 5.65723 8.44531L5.49219 8.59375L3 11.0859V6C3 5.44772 3.44772 5 4 5H15Z"
        fill="#1F1F1F"
      />
      <circle cx="11" cy="7" r="1" fill="#1F1F1F" />
    </svg>
  );
}
