interface Props {
  className?: string;
  size: number;
}
export function ThoughtQuoteIcon({ size, className }: Props) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.5 5C8.98528 5 11 7.01472 11 9.5V12.3271C11 15.2269 8.92618 17.7118 6.07324 18.2305L4.67871 18.4834C4.13537 18.5821 3.61539 18.2221 3.5166 17.6787C3.41786 17.1354 3.77794 16.6154 4.32129 16.5166L5.71582 16.2627C6.77511 16.07 7.67189 15.4698 8.25977 14.6426C7.71918 14.8725 7.12451 15 6.5 15C4.01472 15 2 12.9853 2 10.5V9.5C2 7.01472 4.01472 5 6.5 5ZM17.5 5C19.9853 5 22 7.01472 22 9.5V12.3271C22 15.2269 19.9262 17.7118 17.0732 18.2305L15.6787 18.4834C15.1354 18.5821 14.6154 18.2221 14.5166 17.6787C14.4179 17.1354 14.7779 16.6154 15.3213 16.5166L16.7158 16.2627C17.7751 16.07 18.6719 15.4698 19.2598 14.6426C18.7192 14.8725 18.1245 15 17.5 15C15.0147 15 13 12.9853 13 10.5V9.5C13 7.01472 15.0147 5 17.5 5ZM6.5 7C5.11929 7 4 8.11929 4 9.5V10.5C4 11.8807 5.11929 13 6.5 13C7.88071 13 9 11.8807 9 10.5V9.5C9 8.11929 7.88071 7 6.5 7ZM17.5 7C16.1193 7 15 8.11929 15 9.5V10.5C15 11.8807 16.1193 13 17.5 13C18.8807 13 20 11.8807 20 10.5V9.5C20 8.11929 18.8807 7 17.5 7Z"
        fill="#1F1F1F"
      />
    </svg>
  );
}
