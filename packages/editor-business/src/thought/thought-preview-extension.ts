import { DOC_FRAGMENT_FIELED } from '@repo/editor-common';
import { Collaboration } from '@tiptap/extension-collaboration';
import {
  EditorExtensionOptions,
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
} from '../common';
import type { ExtensionInitContent } from './thought-editor/type';

export type ThoughtPreviewExtensionOptions = {
  initContent: ExtensionInitContent;
  options?: EditorExtensionOptions;
};

export const getThoughtPreviewExtension = (options: ThoughtPreviewExtensionOptions) => {
  const { options: editorOptions, initContent } = options || {};
  return [
    ...getCommonFunctionExtensionList(editorOptions),
    ...getMarkExtensionList(editorOptions),
    ...getNodeExtensionList(editorOptions),
    Collaboration.configure({
      document: initContent.ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
  ];
};
