// 移动端检测函数
export function isMobile(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  // 检测用户代理字符串中是否包含移动设备标识
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android',
    'webos',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
  ];

  const isMobileUserAgent = mobileKeywords.some((keyword) => userAgent.includes(keyword));

  // 检测屏幕宽度（移动端通常小于768px）
  const isMobileScreen = window.innerWidth < 768;

  // 检测是否支持触摸
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // 综合判断：用户代理包含移动设备标识，或者屏幕宽度小于768px且支持触摸
  return isMobileUserAgent || (isMobileScreen && isTouchDevice);
}
