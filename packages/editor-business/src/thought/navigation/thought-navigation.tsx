import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/react';
import { debounce } from 'lodash-es';
import { useCallback, useEffect, useState } from 'react';

import {
  extractHeadingsFromEditor,
  getBgColor,
  getLineWidth,
  type HeadingItem,
  MAX_DETAIL_WIDTH,
  MAX_HEIGHT,
  scrollToHeading,
} from './navigation-utils';
import './thought-navigation.css';

export const ThoughtNavigation = (props: { editor: Editor; position?: 'fixed' | 'absolute' }) => {
  const { editor, position = 'absolute' } = props;
  const [headings, setHeadings] = useState<HeadingItem[]>([]);
  const [isHovered, setIsHovered] = useState(false);

  const extractHeadings = useCallback(() => {
    const headingsList = extractHeadingsFromEditor(editor);
    setHeadings(headingsList);
  }, [editor]);

  // 创建 debounced 版本的 extractHeadings，延迟 800ms
  const debouncedExtractHeadings = useCallback(debounce(extractHeadings, 800), [extractHeadings]);

  // 在组件挂载时获取标题信息，并监听编辑器内容变化
  useEffect(() => {
    // 初始化时获取标题
    extractHeadings();

    // 监听编辑器内容更新事件
    const handleUpdate = () => {
      debouncedExtractHeadings();
    };

    editor.on('update', handleUpdate);

    // 清理函数：移除事件监听器和取消 debounce
    return () => {
      editor.off('update', handleUpdate);
      debouncedExtractHeadings.cancel();
    };
  }, [editor, extractHeadings, debouncedExtractHeadings]);

  const renderPreviewComponent = () => {
    const shouldShow = !isHovered;

    return (
      <div
        className={`thought-navigation__preview ${
          shouldShow
            ? 'thought-navigation__preview--visible'
            : 'thought-navigation__preview--hidden'
        } -my-4 flex flex-col items-start gap-2.5 py-8 pr-4`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {headings.slice(0, 20).map((heading, index) => (
          <div
            key={`${heading.pos}-${index}`}
            className={`rounded-full ${getBgColor(heading.level)} ${getLineWidth(heading.level)} duration-200`}
            style={{
              height: '1px',
            }}
            title={heading.text} // 悬停时显示标题文本
          />
        ))}
      </div>
    );
  };

  const renderDetailComponent = () => {
    // 找到当前文档中的最小标题级别（最高级别的标题）
    const minLevel = headings.length > 0 ? Math.min(...headings.map((h) => h.level)) : 1;

    const shouldShow = isHovered;

    return (
      <div
        className={`thought-navigation__detail ${
          shouldShow ? 'thought-navigation__detail--visible' : 'thought-navigation__detail--hidden'
        } absolute cursor-pointer overflow-y-auto rounded-lg border-none bg-card p-3 shadow-lg`}
        style={{
          maxHeight: `${MAX_HEIGHT}px`,
          maxWidth: `${MAX_DETAIL_WIDTH}px`,
          top: '50%',
          left: '0.5rem',
          transform: 'translateY(-50%)',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex flex-col gap-1">
          {headings.map((heading, index) => {
            // 根据相对于最小级别的差值计算缩进，每级缩进10px
            const indentLevel = (heading.level - minLevel) * 10;

            return (
              <div
                key={`${heading.pos}-${index}`}
                className="rounded px-1 py-0.5 text-sm transition-colors"
                style={{
                  paddingLeft: `${indentLevel}px`,
                }}
                onClick={() => {
                  scrollToHeading(editor, heading);
                }}
              >
                <div
                  className="truncate text-xs transition-colors [color:lch(64.821_1_282.863)] hover:[color:lch(19.643_1_282.863)]"
                  style={{
                    maxWidth: `${MAX_DETAIL_WIDTH - 24 - indentLevel}px`,
                  }}
                  title={heading.text}
                >
                  {heading.text}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (headings.length === 0) {
    return <></>;
  }

  return (
    <div className={cn('left-0.5 top-1/2 -translate-y-1/2', position)}>
      {renderPreviewComponent()}
      {renderDetailComponent()}
    </div>
  );
};
