.thought-navigation {
  position: relative;
}

.thought-navigation__preview {
  transition:
    opacity 350ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 350ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center right;
}

.thought-navigation__preview--hidden {
  opacity: 0;
  transform: translateX(12px) translateY(-4px) scale(1.15);
  pointer-events: none;
}

.thought-navigation__preview--visible {
  opacity: 1;
  transform: translateX(0) translateY(0) scale(1);
}

.thought-navigation__detail {
  transition:
    opacity 400ms cubic-bezier(0.4, 0, 0.2, 1) 50ms,
    transform 400ms cubic-bezier(0.4, 0, 0.2, 1) 50ms;
  transform-origin: center left;
}

.thought-navigation__detail--hidden {
  opacity: 0;
  transform: translateY(-50%) translateX(-8px) scale(0.85);
  pointer-events: none;
}

.thought-navigation__detail--visible {
  opacity: 1;
  transform: translateY(-50%) translateX(0) scale(1);
}
