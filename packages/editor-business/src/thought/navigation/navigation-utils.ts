import type { Level } from '@repo/editor-common';
import { DIFF_CHANGE_ATTR_NAME, DIFF_CHANGE_NAME, DIFF_CHANGE_TYPE } from '@repo/editor-common';
import type { Mark, Node } from '@tiptap/pm/model';
import type { Editor } from '@tiptap/react';

export interface HeadingItem {
  level: Level;
  text: string;
  pos: number;
}

export const MAX_DETAIL_WIDTH = 300;
export const MAX_HEIGHT = 500;

// 根据不同的标题级别渲染不同长度的线条
// level 1 最长，level 6 最短
export const getLineWidth = (level: Level): string => {
  const widths = {
    1: 'w-2.5', // 10px
    2: 'w-1.5', // 6px
    3: 'w-1', // 4px
    4: 'w-1',
    5: 'w-1',
    6: 'w-1',
  };
  return widths[level];
};

// 根据不同的标题级别设置不同的背景颜色
export const getBgColor = (level: Level): string => {
  const colors = {
    1: 'bg-caption', // 一级标题
    2: 'bg-disabled', // 二级标题
    3: 'bg-disabled', // 三级标题及以下都使用 bg-disabled
    4: 'bg-disabled',
    5: 'bg-disabled',
    6: 'bg-disabled',
  };
  return colors[level];
};

// 从节点中提取文本，过滤掉被标记为 DIFF_CHANGE_TYPE.REMOVED 的部分
export const extractCleanTextFromNode = (node: Node): string => {
  let result = '';

  // 遍历节点内容
  node.descendants((childNode: Node) => {
    if (childNode.isText) {
      // 检查文本节点是否有 diff change 标记
      const diffMark = childNode.marks.find((mark: Mark) => mark.type.name === DIFF_CHANGE_NAME);

      if (diffMark) {
        const diffMarkType = diffMark.attrs[DIFF_CHANGE_ATTR_NAME];
        // 如果标记类型不是 REMOVED，则保留这部分文本
        if (diffMarkType !== DIFF_CHANGE_TYPE.REMOVED) {
          result += childNode.text || '';
        }
      } else {
        // 没有 diff change 标记的文本直接保留
        result += childNode.text || '';
      }
    }
  });

  return result;
};

// 从编辑器中提取所有标题
export const extractHeadingsFromEditor = (editor: Editor): HeadingItem[] => {
  const headingsList: HeadingItem[] = [];

  // 遍历编辑器文档，查找所有标题节点
  editor.state.doc.descendants((node, pos) => {
    if (node.type.name === 'heading') {
      const level = node.attrs.level as Level;

      // 从标题节点中提取文本，过滤掉被标记为 REMOVED 的部分
      const text = extractCleanTextFromNode(node);

      if (text.trim()) {
        // 只添加有内容的标题
        headingsList.push({
          level,
          text: text.trim(),
          pos,
        });
      }
    }
  });

  return headingsList;
};

// 滚动到指定标题位置
export const scrollToHeading = (editor: Editor, heading: HeadingItem) => {
  const dom = editor.view.domAtPos(heading.pos + 1);
  if (dom.node) {
    // 滚动到视口中心
    (dom.node as HTMLHeadingElement).scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    });
  }
};
