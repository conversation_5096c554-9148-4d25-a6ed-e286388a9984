import { findViewContainer } from '@repo/editor-business';
import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/react';
import { debounce } from 'lodash-es';
import { AlignRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  extractHeadingsFromEditor,
  type HeadingItem,
  MAX_DETAIL_WIDTH,
  scrollToHeading,
} from './navigation-utils';

import './thought-navigation-mobile.css';

export const ThoughtNavigationMobile = (props: { editor: Editor }) => {
  const { editor } = props;
  const [headings, setHeadings] = useState<HeadingItem[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showDetail, setShowDetail] = useState(false);

  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const componentRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<Element | null>(null);
  const detailRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number>(0);

  const extractHeadings = useCallback(() => {
    const headingsList = extractHeadingsFromEditor(editor);
    setHeadings(headingsList);
  }, [editor]);

  // 创建 debounced 版本的 extractHeadings，延迟 800ms
  const debouncedExtractHeadings = useCallback(debounce(extractHeadings, 800), [extractHeadings]);

  // 清除隐藏定时器
  const clearHideTimer = useCallback(() => {
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
  }, []);

  // 启动隐藏定时器（5秒后隐藏）
  const startHideTimer = useCallback(() => {
    clearHideTimer();
    hideTimerRef.current = setTimeout(() => {
      setShowPreview(false);
      setShowDetail(false);
    }, 1500);
  }, [clearHideTimer]);

  // 处理滚动事件 - 立即显示组件
  const handleScroll = useCallback(() => {
    setShowDetail(false); // 滚动时关闭 detail
    setShowPreview(true);
    debouncedStartHideTimer();
  }, []);

  // 创建 debounced 版本的启动隐藏定时器，延迟 100ms
  const debouncedStartHideTimer = useCallback(debounce(startHideTimer, 100), [startHideTimer]);

  // 处理点击事件
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      componentRef.current &&
      event.target &&
      !componentRef.current.contains(event.target as Node)
    ) {
      setShowDetail(false);
    }
  }, []);

  // 处理触摸事件以阻止移动端滚动透传
  const handleTouchStart = useCallback((e: TouchEvent) => {
    touchStartY.current = e.touches[0]?.clientY || 0;
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!detailRef.current) return;

    const currentY = e.touches[0]?.clientY || 0;
    const deltaY = touchStartY.current - currentY;
    const element = detailRef.current;

    // 检查滚动边界
    const isAtTop = element.scrollTop === 0;
    const isAtBottom = element.scrollTop + element.clientHeight >= element.scrollHeight;

    // 如果向上滚动且已在顶部，或向下滚动且已在底部，则阻止事件传播
    if ((deltaY < 0 && isAtTop) || (deltaY > 0 && isAtBottom)) {
      e.preventDefault();
    }
  }, []);

  // 在组件挂载时获取标题信息，并监听编辑器内容变化
  useEffect(() => {
    // 初始化时获取标题
    extractHeadings();

    // 监听编辑器内容更新事件
    const handleUpdate = () => {
      debouncedExtractHeadings();
    };

    editor.on('update', handleUpdate);

    // 清理函数：移除事件监听器和取消 debounce
    return () => {
      editor.off('update', handleUpdate);
      debouncedExtractHeadings.cancel();
    };
  }, [editor, extractHeadings, debouncedExtractHeadings]);

  // 监听滚动事件
  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;

    // 找到编辑器的滚动容器
    const editorElement = editor.view.dom as Element;
    const scrollContainer = findViewContainer(editorElement as HTMLElement);
    scrollContainerRef.current = scrollContainer;

    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll, {
        passive: true,
      });
    }
    // 同时监听 window 滚动作为备选方案
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    document.addEventListener('click', handleClickOutside);

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);

      debouncedStartHideTimer.cancel();
      clearHideTimer();
    };
  }, [handleScroll, handleClickOutside, clearHideTimer, editor, debouncedStartHideTimer]);

  // 单独处理触摸事件监听器，确保在detail组件渲染后添加
  useEffect(() => {
    if (!showDetail || !detailRef.current) return;

    const element = detailRef.current;

    element.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
    };
  }, [showDetail, handleTouchStart, handleTouchMove]);

  // 每次展开 detail 组件时重置滚动位置到顶部
  useEffect(() => {
    if (showDetail && detailRef.current) {
      // 直接访问滚动容器并重置滚动位置

      detailRef.current.scrollTop = 0;
    }
  }, [showDetail]);

  const renderPreviewComponent = () => {
    const shouldShow = showPreview && !showDetail;

    return (
      <div
        className={cn(
          'thought-navigation-mobile__preview shadow-mdm flex h-[2.875rem] w-8 items-center justify-center rounded-bl-full rounded-tl-full border border-divider bg-card pl-1 shadow-md',
          shouldShow
            ? 'thought-navigation-mobile__preview--visible'
            : 'thought-navigation-mobile__preview--hidden',
        )}
        onClick={() => {
          clearHideTimer();
          setShowDetail(true);
          setShowPreview(false);
        }}
      >
        <AlignRight className="text-foreground" size="20" />
      </div>
    );
  };

  const renderDetailComponent = () => {
    // 找到当前文档中的最小标题级别（最高级别的标题）
    const minLevel = headings.length > 0 ? Math.min(...headings.map((h) => h.level)) : 1;

    const shouldShow = showDetail;

    return (
      <>
        {/* Mask layer */}
        {shouldShow && (
          <div
            className="fixed inset-0 z-[9998] bg-transparent"
            onClick={() => {
              setShowDetail(false);
            }}
          />
        )}

        <div
          className={`thought-navigation-mobile__detail ${
            shouldShow
              ? 'thought-navigation-mobile__detail--visible'
              : 'thought-navigation-mobile__detail--hidden'
          } absolute right-0 z-[9999] h-full w-[16.25rem] cursor-pointer border-none py-4 pl-10`}
          style={{
            top: '50%',
            transform: 'translateY(-50%)',
            background:
              'linear-gradient(90deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.74) 7.96%, #FFF 18.14%, #FFF 100%)',
          }}
        >
          <div
            ref={detailRef}
            className="h-full overflow-y-scroll overscroll-contain"
            onWheel={(e) => {
              // 阻止桌面端滚动事件向外传播
              e.stopPropagation();
            }}
            onMouseEnter={() => {
              clearHideTimer();
            }}
          >
            <div className="flex flex-col justify-center min-h-full gap-6">
              {headings.map((heading, index) => {
                // 根据相对于最小级别的差值计算缩进，每级缩进10px
                const indentLevel = (heading.level - minLevel) * 10;

                return (
                  <div
                    key={`${heading.pos}-${index}`}
                    className="text-sm text-foreground"
                    style={{
                      paddingLeft: `${indentLevel}px`,
                    }}
                    onClick={() => {
                      scrollToHeading(editor, heading);
                      // 点击标题后隐藏detail组件
                      setShowDetail(false);
                    }}
                  >
                    <div
                      className="text-sm truncate"
                      style={{
                        maxWidth: `${MAX_DETAIL_WIDTH - 24 - indentLevel}px`,
                      }}
                      title={heading.text}
                    >
                      {heading.text}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </>
    );
  };

  if (headings.length === 0) {
    return <></>;
  }

  return (
    <div
      className={'flex fixed top-0 right-0 justify-center items-center h-full'}
      ref={componentRef}
    >
      {renderPreviewComponent()}
      {renderDetailComponent()}
    </div>
  );
};
