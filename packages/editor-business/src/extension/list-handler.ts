import {
  BulletList,
  checkIsInList,
  HardBreak,
  ListItem,
  OrderedList,
  Paragraph,
  TaskItem,
  TaskList,
} from '@repo/editor-common';
import { type Editor, Extension } from '@tiptap/core';
import type { Node, ResolvedPos } from '@tiptap/pm/model';
import { Selection } from '@tiptap/pm/state';

export const ListHandler = Extension.create({
  name: 'listHandler',
  priority: 1000,

  addKeyboardShortcuts() {
    return {
      Backspace: ({ editor }) => {
        const { selection } = editor.state;
        const { $from, $to } = selection;

        // 检查是否跨越了多个 list item, 或者不是光标
        if (!$from.sameParent($to) || !selection.empty) {
          return false;
        }

        const isDocStart = $from.pos === 0;

        // 如果在文档开头，不做任何处理
        if (isDocStart) {
          return false;
        }

        // 检查是否在任意类型的列表项内
        const isInList = checkIsInList(editor);

        if (!isInList) {
          return handleBackspaceOnListAfter(editor, $from);
        }

        // 处理列表内的 Backspace
        return handleListBackspace(editor, $from);
      },
      Enter: ({ editor }) => {
        const { selection } = editor.state;
        const { $from } = selection;

        // 检查是否在任意类型的列表项内
        const isInList =
          editor.isActive(ListItem.name) ||
          editor.isActive(TaskItem.name) ||
          editor.isActive(OrderedList.name) ||
          editor.isActive(BulletList.name);

        // 如果不在列表内，不处理
        if (!isInList) {
          return false;
        }

        // 检查整个列表项是否为空
        const isCurrentListItemEmpty = isListItemEmpty($from);

        // 如果列表项为空，删除当前 listitem
        if (isCurrentListItemEmpty) {
          // 尝试退出列表项，根据当前激活的列表类型进行处理
          if (editor.isActive(ListItem.name)) {
            return editor.chain().liftListItem(ListItem.name).run();
          } else if (editor.isActive(TaskItem.name)) {
            return editor.chain().liftListItem(TaskItem.name).run();
          }

          // 如果上述方法都不行，尝试直接转换为普通段落
          return editor
            .chain()
            .command(({ tr }) => {
              const { $from } = tr.selection;

              // 找到当前列表项的范围
              for (let i = 1; i <= $from.depth; i++) {
                const node = $from.node(i);
                if (node.type.name === ListItem.name || node.type.name === TaskItem.name) {
                  const start = $from.start(i) - 1;
                  const end = $from.end(i) + 1;

                  // 创建一个空的段落节点
                  const paragraph = tr.doc.type.schema.nodes[Paragraph.name]?.create();

                  if (!paragraph) {
                    return false;
                  }

                  // 替换列表项为段落
                  tr.replaceWith(start, end, paragraph);

                  // 设置光标位置到新段落的开始
                  tr.setSelection(Selection.near(tr.doc.resolve(start + 1)));

                  return true;
                }
              }

              return false;
            })
            .run();
        }

        // 其他情况不处理，让默认行为生效
        return false;
      },
    };
  },
});

function calculateListDepth(node: Node): number {
  let currentNode = node;
  let depth = 0;

  while (currentNode.lastChild) {
    depth += 1;
    currentNode = currentNode.lastChild;
  }

  return depth;
}

// 检查段落是否为空（过滤掉 HardBreak 节点）
function isParagraphEmptyExcludingHardBreak($from: ResolvedPos): boolean {
  const parent = $from.parent;

  if (parent.type.name !== Paragraph.name) {
    return false;
  }

  if (parent.content.size === 0) return true;

  // 过滤掉 HardBreak 节点后检查是否为空
  let nonHardBreakContent = 0;
  parent.content.forEach((node) => {
    if (node.type.name !== HardBreak.name) {
      nonHardBreakContent += node.nodeSize;
    }
  });
  return nonHardBreakContent === 0;
}

// 检查列表项是否为空（考虑多个段落的情况）
function isListItemEmpty($from: ResolvedPos): boolean {
  // 找到列表项节点
  let listItemNode = null;

  for (let i = 1; i <= $from.depth; i++) {
    const node = $from.node(i);
    if (node.type.name === ListItem.name || node.type.name === TaskItem.name) {
      listItemNode = node;
      break;
    }
  }

  if (!listItemNode) {
    return false;
  }

  // 检查列表项的所有子节点是否都为空
  for (let i = 0; i < listItemNode.childCount; i++) {
    const child = listItemNode.child(i);

    if (child.type.name === Paragraph.name) {
      // 检查段落是否为空
      const textContent = child.textContent;
      const hasOnlyTrailingBreak =
        child.content.size === 1 &&
        child.firstChild &&
        child.firstChild.type.name === HardBreak.name;

      if (textContent.trim() !== '' && !hasOnlyTrailingBreak) {
        return false;
      }
    } else if (child.content.size > 0) {
      // 非段落节点且有内容
      return false;
    }
  }

  return true;
}

function handleListBackspace(editor: Editor, $from: ResolvedPos): boolean {
  // 检查是否在段落的开头
  if ($from.parentOffset !== 0) {
    return false;
  }

  // 检查当前段落是否是 listitem 的第一个子元素
  let listItemDepth = -1;
  for (let i = $from.depth; i >= 0; i--) {
    const node = $from.node(i);
    if (node.type.name === ListItem.name || node.type.name === TaskItem.name) {
      listItemDepth = i;
      break;
    }
  }

  if (listItemDepth === -1) {
    return false;
  }

  const currentParagraphIndex = $from.index(listItemDepth);

  // 只有当前段落是 listitem 的第一个子元素时才退出列表项
  if (currentParagraphIndex !== 0) {
    return false;
  }

  // 尝试退出列表项，根据当前激活的列表类型进行处理
  if (editor.isActive(ListItem.name)) {
    return editor.chain().liftListItem(ListItem.name).run();
  } else if (editor.isActive(TaskItem.name)) {
    return editor.chain().liftListItem(TaskItem.name).run();
  }

  return false;
}

const handleBackspaceOnListAfter = (editor: Editor, $from: ResolvedPos): boolean => {
  // 检查是否在文档开头，如果是则没有前一个节点
  if ($from.pos <= 1 || $from.parentOffset !== 0) {
    return false;
  }

  // 更可靠地获取前一个节点
  let prevNode = null;
  let prevNodePos = $from.pos - 1;

  // 尝试从当前位置往前查找，直到找到一个有效的节点
  while (prevNodePos > 0 && !prevNode) {
    try {
      const $prevPos = editor.state.doc.resolve(prevNodePos);

      // 尝试获取前一个节点
      if ($prevPos.nodeBefore) {
        prevNode = $prevPos.nodeBefore;
        break;
      }

      // 如果 nodeBefore 为空，尝试获取父节点的前一个兄弟节点
      for (let depth = $prevPos.depth; depth >= 0; depth--) {
        const index = $prevPos.index(depth);
        const parent = $prevPos.node(depth);

        if (index > 0) {
          prevNode = parent.child(index - 1);
          break;
        }
      }

      // 如果还是没找到，继续往前一个位置查找
      if (!prevNode) {
        prevNodePos--;
      }
    } catch (e) {
      // 如果位置无效，继续往前查找
      prevNodePos--;
    }
  }

  if (!prevNode) {
    return false;
  }

  const prevNodeType = prevNode.type.name;
  if (![BulletList.name, OrderedList.name, TaskList.name].includes(prevNodeType)) {
    return false;
  }
  const isEmptyParagraph = isParagraphEmptyExcludingHardBreak($from);
  const depth = calculateListDepth(prevNode);
  const lastListItemPos = $from.before() - depth;

  if (isEmptyParagraph) {
    editor
      .chain()
      .command(({ tr }) => {
        tr.delete($from.before(), $from.after());
        tr.setSelection(Selection.near(tr.doc.resolve(lastListItemPos)));
        return true;
      })
      .run();
    return true;
  }

  // 如果当前段落不为空，将内容插入到列表的最后一个列表项
  return editor
    .chain()
    .command(({ tr }) => {
      // 获取当前段落的内容
      const currentParagraphContent = $from.parent.content;

      // 找到列表的最后一个列表项的位置
      const $lastListItemPos = tr.doc.resolve(lastListItemPos);

      // 删除原来的段落
      tr.delete($from.before(), $from.after());

      // 将当前段落的内容插入到最后一个列表项的末尾
      tr.insert($lastListItemPos.end(), currentParagraphContent);

      tr.setSelection(Selection.near(tr.doc.resolve(lastListItemPos)));

      return true;
    })
    .run();
};
