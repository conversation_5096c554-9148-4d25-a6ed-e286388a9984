import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';

export const EditorStatusClassExtensionName = 'editorStatusClassExtension';

export const EditorStatusClassExtension = Extension.create({
  name: EditorStatusClassExtensionName,

  addProseMirrorPlugins() {
    const key = new PluginKey('EditorStatusClassName');

    return [
      new Plugin({
        key,
        view: (view) => {
          const updateEditorStatusClassName = () => {
            const isComposing = view.composing;
            const editorElement = view.dom as HTMLElement;
            const isFocus = view.hasFocus();

            if (isComposing) {
              editorElement.classList.add('youmind-editor-composing');
            } else {
              editorElement.classList.remove('youmind-editor-composing');
            }

            if (isFocus) {
              editorElement.classList.add('youmind-editor-focused');
            } else {
              editorElement.classList.remove('youmind-editor-focused');
            }
          };

          updateEditorStatusClassName();

          const handleCompositionStart = () => {
            setTimeout(() => updateEditorStatusClassName(), 0);
          };

          const handleCompositionEnd = () => {
            setTimeout(() => updateEditorStatusClassName(), 0);
          };

          const handleFocus = () => {
            updateEditorStatusClassName();
          };

          const handleBlur = () => {
            updateEditorStatusClassName();
          };

          view.dom.addEventListener('compositionstart', handleCompositionStart);
          view.dom.addEventListener('compositionend', handleCompositionEnd);
          view.dom.addEventListener('focus', handleFocus);
          view.dom.addEventListener('blur', handleBlur);

          const update = () => {
            updateEditorStatusClassName();
            return true;
          };

          const destroy = () => {
            view.dom.removeEventListener('compositionstart', handleCompositionStart);
            view.dom.removeEventListener('compositionend', handleCompositionEnd);
            view.dom.removeEventListener('focus', handleFocus);
            view.dom.removeEventListener('blur', handleBlur);
          };

          return {
            update,
            destroy,
          };
        },
      }),
    ];
  },
});
