import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/core';
import { Check } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { formatUrl, MAX_URL_LENGTH, updateEditorLink } from './utils';

interface LinkUrlEditPanelProps {
  editor: Editor;
  closePanel: () => void;
  initialPosition?: { top: number; left: number };
}

export function LinkUrlEditPanel({ editor, closePanel, initialPosition }: LinkUrlEditPanelProps) {
  const [url, setUrl] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const [position, setPosition] = useState(() => {
    if (initialPosition) return initialPosition;
    return { top: 0, left: 0 };
  });

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current?.focus();
    }
  }, []);

  const handleUrlChange = (value: string) => {
    setUrl(value);
  };

  const handleSubmit = () => {
    const formattedUrl = formatUrl(url);

    // 获取当前选区的文本作为链接文本
    const { state } = editor;
    const selectedText = state.doc.textBetween(state.selection.from, state.selection.to, '');

    updateEditorLink({
      editor,
      url: formattedUrl,
      title: selectedText || formattedUrl, // 如果没有选中文本，使用 URL 作为链接文本
    });
    closePanel();
  };

  return (
    <div onClick={closePanel} className="fixed top-0 left-0 z-50 w-full h-full">
      <div
        onClick={(e) => e.stopPropagation()}
        style={{
          position: 'absolute',
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
        className="flex h-10 w-[20.5rem] items-center justify-between rounded-lg bg-card shadow-lg"
      >
        <input
          maxLength={MAX_URL_LENGTH}
          ref={inputRef}
          type="text"
          placeholder="Paste a link"
          autoComplete="off"
          onChange={(e) => handleUrlChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleSubmit();
            }
          }}
          className="flex-1 py-2 pr-2 pl-4 text-sm bg-transparent outline-none text-muted-foreground placeholder:text-disabled"
        />
        <div className="h-full w-[1px] bg-divider" />
        <div onClick={handleSubmit} className={'flex justify-center items-center w-10 h-full'}>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handleSubmit();
            }}
            className={cn(
              'flex justify-center items-center w-4 h-4 rounded-full',
              'text-brand-foreground',
              url ? 'bg-brand' : 'bg-brand/50',
            )}
          >
            <Check size={9} />
          </div>
        </div>
      </div>
    </div>
  );
}
