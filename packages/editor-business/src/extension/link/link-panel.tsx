import type { LinkOptions } from '@repo/editor-common';
import {
  Too<PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { cn } from '@repo/ui/lib/utils';
import type { Editor } from '@tiptap/core';
import { Copy, ExternalLink, Pen, Trash } from 'lucide-react';
import { useCallback, useRef } from 'react';

interface LinkPanelProps {
  editor: Editor;
  link: HTMLAnchorElement;
  hide: () => void;
  openLinkEditPanel: (containerRect: DOMRect, link: HTMLAnchorElement) => void;
  options?: LinkOptions['hoverMenuOptions'];
}

const buttonBaseStyles =
  'flex h-[1.75rem] items-center rounded-lg px-[0.45rem] hover:bg-muted hover:text-muted-foreground';

export const LinkPanel = ({ editor, link, hide, openLinkEditPanel, options }: LinkPanelProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const findLinkPosition = useCallback(() => {
    if (!link) return null;

    try {
      const view = editor.view;
      const pos = view.posAtDOM(link, 0);

      if (pos !== null && pos >= 0) {
        const node = view.state.doc.nodeAt(pos);
        return node ? { from: pos, to: pos + node.nodeSize } : null;
      }

      const parent = link.parentNode as HTMLElement;
      if (!parent) return null;

      const parentPos = view.posAtDOM(parent, 0);
      if (parentPos === null || parentPos < 0) return null;

      let offset = 0;
      for (const child of Array.from(parent.childNodes)) {
        if (child === link) break;
        offset += child.textContent?.length || 0;
      }

      const finalPos = parentPos + offset;
      const node = view.state.doc.nodeAt(finalPos);
      return node ? { from: finalPos, to: finalPos + node.nodeSize } : null;
    } catch (error) {
      console.error('Error finding link position:', error);
      return null;
    }
  }, [editor, link]);

  const removeLink = useCallback(() => {
    if (!link) return;

    try {
      const position = findLinkPosition();
      if (!position) {
        console.warn('Could not find valid link position');
        return;
      }

      editor
        .chain()
        .setTextSelection(position)
        .unsetLink()
        .setTextSelection({ from: position.to, to: position.to })
        .run() && hide?.();
    } catch (error) {
      console.error('Error removing link:', error);
    }
  }, [editor, findLinkPosition, link, hide]);

  const handleCopyLink = () => {
    const href = link?.getAttribute('href');
    if (href) {
      if (options?.copyLink?.handleCopyLink) {
        options.copyLink.handleCopyLink({ url: href, editor });
      } else {
        navigator.clipboard.writeText(href).then(() => {
          // success('Copied to clipboard');
          hide?.();
        });
      }
    }
  };
  const showEditPanel = useCallback(() => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      openLinkEditPanel(rect, link);
    }
    hide();
  }, [hide, openLinkEditPanel, link]);

  const handleOpenLink = () => {
    if (options?.openLink?.handleOpenLink) {
      options.openLink.handleOpenLink({
        url: link?.getAttribute('href') || '',
        editor,
      });
    } else {
      window.open(link?.getAttribute('href') || '', '_blank');
    }
  };

  return (
    <div
      ref={containerRef}
      className="flex h-10 w-[17.5rem] items-center justify-between rounded-[inherit] bg-card py-0.5 pl-3 pr-[0.25rem]"
    >
      <div className="overflow-hidden text-sm whitespace-nowrap text-ellipsis text-muted-foreground">
        {link?.getAttribute('href') || <div className="text-disabled">Empty link</div>}
      </div>
      <div className="flex items-center">
        <div className="ml-2 mr-[0.5rem] h-5 w-[1px] bg-divider" />
        <div className="flex items-center justify-between">
          {options?.openLink?.enable && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    disabled={!link?.getAttribute('href')}
                    onClick={handleOpenLink}
                    className={cn(buttonBaseStyles)}
                    aria-label="Open link"
                  >
                    <ExternalLink className="text-muted-foreground" size={14} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{link?.getAttribute('href') ? 'Open Link' : 'Empty link'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {options?.copyLink?.enable && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    disabled={!link?.getAttribute('href')}
                    onClick={handleCopyLink}
                    className={cn(buttonBaseStyles)}
                    aria-label="Copy link"
                  >
                    <Copy className="text-muted-foreground" size={14} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{link?.getAttribute('href') ? 'Copy' : 'Empty link'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className={cn(buttonBaseStyles)}
                  aria-label="Edit link"
                  onClick={showEditPanel}
                >
                  <Pen className="text-muted-foreground" size={14} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={removeLink}
                  className={cn(buttonBaseStyles)}
                  aria-label="Remove link"
                >
                  <Trash className="text-muted-foreground" size={14} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Remove link</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};
