import { Button } from '@repo/ui/components/ui/button';
import { Input } from '@repo/ui/components/ui/input';
import type { Editor } from '@tiptap/react';
import { useState } from 'react';

import { formatUrl, MAX_URL_LENGTH, updateEditorLink } from './utils';

interface LinkEditPanelProps {
  editor: Editor;
  link: HTMLAnchorElement;
  closePanel: () => void;
  containerRect: DOMRect;
  initialPosition?: { top: number; left: number };
}

export function LinkEditPanel({
  editor,
  link,
  closePanel,
  containerRect,
  initialPosition,
}: LinkEditPanelProps) {
  const [url, setUrl] = useState(link.getAttribute('href') || '');
  const [title, setTitle] = useState(link.textContent || '');
  const [position, setPosition] = useState(() => {
    if (initialPosition) return initialPosition;

    const rect = link.getBoundingClientRect();
    const panelWidth = 360; // 22.5rem
    const panelHeight = 208; // 13rem

    let top = rect.bottom + 8; // 8px gap
    let left = containerRect.left;

    if (top + panelHeight > window.innerHeight) {
      top = rect.top - panelHeight - 8;
    }

    if (left + panelWidth > window.innerWidth) {
      left = window.innerWidth - panelWidth - 16;
    }

    left = Math.max(16, left);

    return { top, left };
  });

  const handleSubmit = () => {
    const formattedUrl = formatUrl(url);
    updateEditorLink({
      editor,
      url: formattedUrl,
      title,
      link,
    });
    closePanel();
  };

  return (
    <div onClick={closePanel} className="fixed top-0 left-0 z-50 w-full h-full">
      <div
        onClick={(e) => e.stopPropagation()}
        style={{
          position: 'absolute',
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
        className="flex h-[13rem] w-[22.5rem] flex-col rounded-[inherit] rounded-lg bg-card p-4 shadow-lg"
      >
        <div className="mb-[1rem]">
          <div className="mb-[0.5rem] text-sm text-caption">URL</div>
          <Input
            maxLength={MAX_URL_LENGTH}
            autoComplete="off"
            id="url"
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="Enter URL"
          />
        </div>

        <div className="mb-[1rem]">
          <div className="mb-[0.5rem] text-sm text-caption">Link Title</div>
          <Input
            maxLength={MAX_URL_LENGTH}
            autoComplete="off"
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter link title"
          />
        </div>

        <Button
          onClick={handleSubmit}
          type="submit"
          size="sm"
          disabled={!url.trim()}
          className="self-start"
        >
          Done
        </Button>
      </div>
    </div>
  );
}
