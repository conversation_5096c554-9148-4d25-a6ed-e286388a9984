import type { LinkOptions } from '@repo/editor-common';
import type { Editor } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import type { EditorView } from '@tiptap/pm/view';
import { LinkPanelController } from './link-panel-controller';

export interface LinkPanelState {
  linkPanelController: LinkPanelController;
}

export const linkPanelPluginKey = new PluginKey<LinkPanelState>('link-panel');

export const createLinkPanelPlugin = (editor: Editor, options: LinkOptions) => {
  return new Plugin<LinkPanelState>({
    key: linkPanelPluginKey,
    state: {
      init() {
        return {
          linkPanelController: new LinkPanelController(editor, options),
        };
      },
      apply(tr, value) {
        return value;
      },
    },
    view(view) {
      return {
        update(view: EditorView, prevState) {
          const currentState = view.state;
          if (!prevState.selection.eq(currentState.selection) && !currentState.selection.empty) {
            const pluginState = linkPanelPluginKey.getState(currentState);
            if (pluginState) {
              pluginState.linkPanelController.hideHoverPanel();
            }
          }
        },
        destroy() {
          const state = linkPanelPluginKey.getState(view.state);
          if (state) {
            state.linkPanelController.destroy();
          }
        },
      };
    },
  });
};
