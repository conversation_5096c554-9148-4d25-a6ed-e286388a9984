.node-diffBlock {
  margin: 0.6rem 0;
}

.diff-block-wrapper {
  position: relative;
  border-radius: 4px;
  transition:
    box-shadow 0.2s ease,
    background-color 0.2s ease;
}

/* 使用伪元素扩大左右hover热区，避免干扰左侧导航 */
.diff-block-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1rem;
  right: -4rem;
  bottom: 0;
  pointer-events: auto;
}

.diff-operation-buttons {
  position: absolute;
  top: -36px;
  right: 0px;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.2s ease,
    visibility 0.2s ease;
}

.diff-operation-buttons::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -15px;
  right: 0;
  bottom: 0;
  z-index: -1;
  pointer-events: auto;
}

.diff-block-wrapper::before {
  content: "";
  position: absolute;
  top: -0.5rem;
  left: -0.5rem;
  right: -0.5rem;
  bottom: -0.5rem;
  pointer-events: auto;
}
