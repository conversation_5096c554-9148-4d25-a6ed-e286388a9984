import {
  addSelectionNodes,
  base64ToJSON,
  DIFF_CHANGE_TYPE,
  DiffBlock as DiffBlockBase,
  type DiffBlockOptions,
  diffTransformUtils,
  markdownSerializer,
  removeAllSelectionNodes,
} from '@repo/editor-common';
import type { Editor, RawCommands } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { DiffBlockComponent } from './diff-block-component';
import { DiffBlockManage } from './diff-block-manage';

// todo
type ThoughtVO = any;

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    diffBlock: {
      getEditorAIWriterMarkdownContent: () => ReturnType;
      clearViewSelection: () => ReturnType;
    };
    setAIWriterContent: {
      setAIWriterContent: (data: ThoughtVO) => ReturnType;
    };
  }
}

interface DiffBlockStorage {
  diffBlockManage?: DiffBlockManage;
}

export interface DiffBlockAttributes {
  diffBlockId: string;
}

export const DiffBlock = DiffBlockBase.extend<DiffBlockOptions, DiffBlockStorage>({
  addStorage() {
    return {
      diffBlockManage: undefined,
    };
  },

  onCreate() {
    if (!this.options.DiffBlockManageEnable) {
      return;
    }
    this.storage.diffBlockManage = new DiffBlockManage(this.editor);
    setTimeout(() => {
      // 因为目前有本地的数据同步，所以需要在本地数据同步完成之后再初始化 diff-block 管理器
      this.storage.diffBlockManage?.init();
      const hasDiffBlock = this.storage.diffBlockManage?.checkHasDiffBlock();
      if (hasDiffBlock) {
        this.storage.diffBlockManage?.showManageToolbar();
      }
    }, 300);
  },

  addNodeView() {
    return ReactNodeViewRenderer(DiffBlockComponent);
  },

  addKeyboardShortcuts() {
    return {
      Backspace: ({ editor }) => {
        const { selection } = editor.state;
        const { $from, $to } = selection;

        // 检查选区是否包含或接触 diff-block 节点
        let hasDiffBlock = false;

        // 检查选区范围内是否有 diff-block
        if (!selection.empty) {
          editor.state.doc.nodesBetween($from.pos, $to.pos, (node) => {
            if (node.type.name === this.name) {
              hasDiffBlock = true;
              return false; // 停止遍历
            }
            return true;
          });
        }

        // 检查光标前是否是 diff-block
        if (selection.empty) {
          // 向前查找最近的非 null 节点
          let depth = $from.depth;
          let nodeBefore = $from.nodeBefore;

          // 如果当前层级的 nodeBefore 为 null，则继续向外层查找
          while (depth > 0 && nodeBefore === null) {
            depth -= 1;
            const pos = $from.before(depth + 1);
            nodeBefore = editor.state.doc.resolve(pos).nodeBefore;
          }

          // 检查找到的节点是否是 diff-block
          if (nodeBefore && nodeBefore.type.name === this.name) {
            hasDiffBlock = true;
          }
        }

        // 如果存在 diff-block，阻止删除操作
        if (hasDiffBlock) {
          return true; // 返回 true 表示处理了这个快捷键，不再执行默认行为
        }

        // 否则让默认的 backspace 行为生效
        return false;
      },
    };
  },

  addCommands() {
    return {
      getEditorAIWriterMarkdownContent:
        () =>
        ({ editor }) => {
          addSelectionNodes(editor);
          const docWithoutDiffInfo = diffTransformUtils.extractContent(
            editor.state.doc,
            DIFF_CHANGE_TYPE.ADDED,
          );
          const content = markdownSerializer.serialize(docWithoutDiffInfo);
          removeAllSelectionNodes(editor);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          return content as any;
        },
      setAIWriterContent:
        (data: ThoughtVO) =>
        ({ editor }) => {
          const { title, content } = data;
          // const workflow = getWorkflow(editor);
          // if (!workflow) {
          //   return false;
          // }
          // if (title) {
          //   workflow.editTitle(title);
          // }
          if (content) {
            const jsonContent = base64ToJSON(content.raw);
            editor.commands.setContent(jsonContent);
            editor.emit('update', { editor, transaction: editor.state.tr });
            this.storage.diffBlockManage?.reRenderDiffInfo();
          }
          return true;
        },
      clearViewSelection:
        () =>
        ({ editor }) => {
          // 获取当前选区位置
          const { from } = editor.state.selection;

          // 如果当前有选中内容，将光标移动到选区开始位置
          if (!editor.state.selection.empty) {
            editor.commands.setTextSelection(from);
          }

          return true;
        },
    } as Partial<RawCommands>;
  },
});

// 提供一个外部函数来获取编辑器内容
export const getWriterContent = (editor: Editor): string => {
  const docWithoutDiffInfo = diffTransformUtils.extractContent(
    editor.state.doc,
    DIFF_CHANGE_TYPE.REMOVED,
  );
  return markdownSerializer.serialize(docWithoutDiffInfo);
};
