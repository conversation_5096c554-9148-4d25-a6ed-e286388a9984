import { TableCell, TableHeader } from '@repo/editor-common';
import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';

export const SafariTableAdapterExtensionName = 'safariTableAdapterExtension';

export const SafariTableAdapterExtension = Extension.create({
  name: SafariTableAdapterExtensionName,

  addProseMirrorPlugins() {
    const key = new PluginKey(SafariTableAdapterExtensionName);

    return [
      new Plugin({
        key,
        view: (view) => {
          const handleCompositionStart = () => {
            // 检查当前位置是否在空的表格单元格中，如果是则插入零宽字符来修复 WebKit IME 输入 bug
            const { state, dispatch } = view;
            const { selection } = state;
            const { $from } = selection;

            // 检查当前节点是否是表格单元格（th 或 td）
            const isInTableCell =
              this.editor.isActive(TableCell.name) || this.editor.isActive(TableHeader.name);

            if (isInTableCell) {
              // 检查单元格是否为空
              const cellNode = $from.parent;
              const isEmpty = cellNode.textContent.trim() === '';

              if (isEmpty) {
                // 在空单元格中插入零宽字符
                const tr = state.tr.insertText('\u200B', selection.from);
                dispatch(tr);
              }
            }
          };

          view.dom.addEventListener('compositionstart', handleCompositionStart);

          const destroy = () => {
            view.dom.removeEventListener('compositionstart', handleCompositionStart);
          };

          return {
            destroy,
          };
        },
      }),
    ];
  },
});
