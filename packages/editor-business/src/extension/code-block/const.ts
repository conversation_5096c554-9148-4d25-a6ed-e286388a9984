import bash from 'highlight.js/lib/languages/bash';
import c from 'highlight.js/lib/languages/c';
import cpp from 'highlight.js/lib/languages/cpp';
import csharp from 'highlight.js/lib/languages/csharp';
import css from 'highlight.js/lib/languages/css';
import go from 'highlight.js/lib/languages/go';
import java from 'highlight.js/lib/languages/java';
import js from 'highlight.js/lib/languages/javascript';
import json from 'highlight.js/lib/languages/json';
import kotlin from 'highlight.js/lib/languages/kotlin';
import markdown from 'highlight.js/lib/languages/markdown';
import php from 'highlight.js/lib/languages/php';
import plaintext from 'highlight.js/lib/languages/plaintext';
import powershell from 'highlight.js/lib/languages/powershell';
import python from 'highlight.js/lib/languages/python';
import ruby from 'highlight.js/lib/languages/ruby';
import rust from 'highlight.js/lib/languages/rust';
import sql from 'highlight.js/lib/languages/sql';
import swift from 'highlight.js/lib/languages/swift';
import ts from 'highlight.js/lib/languages/typescript';
import html from 'highlight.js/lib/languages/xml';
import yaml from 'highlight.js/lib/languages/yaml';

export const CodeBlockLanguages = {
  bash: {
    label: 'Bash',
    value: 'bash',
  },
  c: {
    label: 'C',
    value: 'c',
  },
  cpp: {
    label: 'C++',
    value: 'cpp',
  },
  csharp: {
    label: 'C#',
    value: 'csharp',
  },
  css: {
    label: 'CSS',
    value: 'css',
  },
  go: {
    label: 'Go',
    value: 'go',
  },
  html: {
    label: 'HTML',
    value: 'html',
  },
  java: {
    label: 'Java',
    value: 'java',
  },
  javascript: {
    label: 'JavaScript',
    value: 'javascript',
  },
  json: {
    label: 'JSON',
    value: 'json',
  },
  kotlin: {
    label: 'Kotlin',
    value: 'kotlin',
  },
  markdown: {
    label: 'Markdown',
    value: 'markdown',
  },
  mermaid: {
    label: 'Mermaid',
    value: 'mermaid',
  },
  php: {
    label: 'PHP',
    value: 'php',
  },
  plaintext: {
    label: 'Plain Text',
    value: 'plaintext',
  },
  powershell: {
    label: 'PowerShell',
    value: 'powershell',
  },
  python: {
    label: 'Python',
    value: 'python',
  },
  ruby: {
    label: 'Ruby',
    value: 'ruby',
  },
  rust: {
    label: 'Rust',
    value: 'rust',
  },
  sql: {
    label: 'SQL',
    value: 'sql',
  },
  swift: {
    label: 'Swift',
    value: 'swift',
  },
  typescript: {
    label: 'TypeScript',
    value: 'typescript',
  },
  yaml: {
    label: 'YAML',
    value: 'yaml',
  },
  xml: {
    label: 'XML',
    value: 'xml',
  },
};

export const HightLightMap = {
  [CodeBlockLanguages.bash.value]: bash,
  [CodeBlockLanguages.c.value]: c,
  [CodeBlockLanguages.cpp.value]: cpp,
  [CodeBlockLanguages.csharp.value]: csharp,
  [CodeBlockLanguages.css.value]: css,
  [CodeBlockLanguages.go.value]: go,
  [CodeBlockLanguages.html.value]: html,
  [CodeBlockLanguages.java.value]: java,
  [CodeBlockLanguages.javascript.value]: js,
  [CodeBlockLanguages.json.value]: json,
  [CodeBlockLanguages.kotlin.value]: kotlin,
  [CodeBlockLanguages.markdown.value]: markdown,
  [CodeBlockLanguages.php.value]: php,
  [CodeBlockLanguages.plaintext.value]: plaintext,
  [CodeBlockLanguages.powershell.value]: powershell,
  [CodeBlockLanguages.python.value]: python,
  [CodeBlockLanguages.ruby.value]: ruby,
  [CodeBlockLanguages.rust.value]: rust,
  [CodeBlockLanguages.sql.value]: sql,
  [CodeBlockLanguages.swift.value]: swift,
  [CodeBlockLanguages.typescript.value]: ts,
  [CodeBlockLanguages.yaml.value]: yaml,
  [CodeBlockLanguages.xml.value]: html,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const registerLanguages = (lowlight: any) => {
  // 通过 CodeBlockLanguages 和 HightLightMap 动态注册语言
  Object.values(CodeBlockLanguages)
    // 不注册 mermaid 语言
    .filter((language) => language.value !== CodeBlockLanguages.mermaid.value)
    .forEach((language) => {
      const highlightModule = HightLightMap[language.value];
      if (highlightModule) {
        // 使用 highlightKey 作为注册名称
        lowlight.register(language.value, highlightModule);
      } else {
        console.warn(`Language ${language.value} not found in HightLightMap`);
      }
    });
};

export const CODEBLOCK_LANGUAGES = Object.values(CodeBlockLanguages);

// 语言别名映射表：将常见简写转换为完整语言名称
export const languageMap: Record<string, string> = {
  // JavaScript
  js: 'javascript',
  jsx: 'javascript',
  es6: 'javascript',
  es: 'javascript',
  node: 'javascript',

  // TypeScript
  ts: 'typescript',
  tsx: 'typescript',

  // Python
  py: 'python',
  python3: 'python',
  py3: 'python',

  // Ruby
  rb: 'ruby',

  // Bash/Shell
  sh: 'bash',
  shell: 'bash',
  zsh: 'bash',
  fish: 'bash',

  // YAML
  yml: 'yaml',

  // Markdown
  md: 'markdown',

  // HTML
  htm: 'html',
  xhtml: 'html',

  // CSS
  scss: 'css',
  sass: 'css',
  less: 'css',
  stylus: 'css',

  // C++
  'c++': 'cpp',
  cxx: 'cpp',
  cc: 'cpp',
  hpp: 'cpp',

  // C#
  cs: 'csharp',
  'c#': 'csharp',
  dotnet: 'csharp',

  // PowerShell
  ps1: 'powershell',
  pwsh: 'powershell',
  posh: 'powershell',

  // Java
  jav: 'java',

  // Kotlin
  kt: 'kotlin',

  // Go
  golang: 'go',

  // Rust
  rs: 'rust',

  // Swift
  ios: 'swift',

  // SQL
  mysql: 'sql',
  postgresql: 'sql',
  postgres: 'sql',
  sqlite: 'sql',
  oracle: 'sql',
  mssql: 'sql',
  tsql: 'sql',
  plsql: 'sql',

  // PHP
  php3: 'php',
  php4: 'php',
  php5: 'php',
  php7: 'php',
  php8: 'php',

  // XML
  xml: 'html', // 在 const.ts 中 xml 映射到 html 高亮器
  svg: 'html',

  // JSON
  jsonc: 'json',
  json5: 'json',

  // Plain text alternatives
  txt: 'plaintext',
  text: 'plaintext',
  plain: 'plaintext',

  // Other common aliases
  makefile: 'bash',
  dockerfile: 'bash',
  conf: 'plaintext',
  config: 'plaintext',
  ini: 'plaintext',
  properties: 'plaintext',
  env: 'plaintext',
  log: 'plaintext',
};
