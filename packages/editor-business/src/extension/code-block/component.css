.tiptap {
  .youmind-code-block {
    background-color: rgb(241, 239, 238);
    color: rgb(104, 97, 94);

    .diff-change-removed {
      color: unset !important;
    }

    pre {
      font-family: "JetBrainsMono", "Fira Code", "Cascadia Code", monospace;
      position: relative;
      overflow-x: auto;
      transition: all 0.3s ease;
      -webkit-spellcheck: false;
      -moz-spellcheck: false;
      background-color: transparent;
      color: #999;

      code {
        background: none;
        color: inherit;
        font-size: 0.875rem;
        line-height: 1.7;
        padding: 0;
        font-weight: 400;
        letter-spacing: 0.01em;
        -webkit-spellcheck: false;
        -moz-spellcheck: false;
      }
      /* 从这里开始写： */

      .hljs-comment {
        color: #766e6b;
      }

      .hljs-quote {
        color: #766e6b;
      }

      .hljs-variable {
        color: #f22c40;
      }

      .hljs-template-variable {
        color: #f22c40;
      }

      .hljs-attribute {
        color: #f22c40;
      }

      .hljs-tag {
        color: #f22c40;
      }

      .hljs-name {
        color: #f22c40;
      }

      .hljs-regexp {
        color: #f22c40;
      }

      .hljs-link {
        color: #f22c40;
      }

      .hljs-selector-id {
        color: #f22c40;
      }

      .hljs-selector-class {
        color: #f22c40;
      }

      .hljs-number {
        color: #df5320;
      }

      .hljs-meta {
        color: #df5320;
      }

      .hljs-built_in {
        color: #df5320;
      }

      .hljs-builtin-name {
        color: #df5320;
      }

      .hljs-literal {
        color: #df5320;
      }

      .hljs-type {
        color: #df5320;
      }

      .hljs-params {
        color: #df5320;
      }

      .hljs-string {
        color: #7b9726;
      }

      .hljs-symbol {
        color: #7b9726;
      }

      .hljs-bullet {
        color: #7b9726;
      }

      .hljs-title {
        color: #407ee7;
      }

      .hljs-section {
        color: #407ee7;
      }

      .hljs-keyword {
        color: #6666ea;
      }

      .hljs-selector-tag {
        color: #6666ea;
      }

      .hljs {
        display: block;
        overflow-x: auto;
        background: #f1efee;
        color: #68615e;
        padding: 0.5em;
      }

      .hljs-emphasis {
        font-style: italic;
      }

      .hljs-strong {
        font-weight: bold;
      }
    }
  }
}
