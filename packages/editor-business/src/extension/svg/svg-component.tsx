import { DIFF_CHANGE_ATTR_NAME, DIFF_CHANGE_TYPE } from '@repo/editor-common';
import SVGEditor from '@repo/ui/components/custom/svg-editor';
import { cn } from '@repo/ui/lib/utils';
import { type NodeViewProps, NodeViewWrapper } from '@tiptap/react';

// import { useEffect } from "react";

// import { getSVGWidthAndHeight } from "@/lib/util/formatSVG";

// import { useDrag } from "../image/display-image-node";

export const SVGComponent: React.FC<NodeViewProps> = (props: NodeViewProps) => {
  const { updateAttributes, editor } = props;
  const { attrs } = props.node;
  const {
    src,
    alt,
    // width: imageDisplayWidth,
    // height: imageDisplayHeight,
  } = attrs;

  // const {
  //   isDragging,
  //   dragSide,
  //   displayRatio,
  //   imageContainerRef,
  //   setDisplayRatio,
  //   onDragImageSize,
  // } = useDrag({
  //   imageDisplayWidth,
  //   imageDisplayHeight,
  //   maxImageHeight: Infinity,
  //   updateAttributes,
  // });

  // useEffect(() => {
  //   fetch(src).then((res) => {
  //     res.text().then((svg) => {
  //       const { width, height } = getSVGWidthAndHeight(svg);
  //       setDisplayRatio(width && height ? Number(width) / Number(height) : 1);
  //     });
  //   });
  // }, [src]);

  // const handleSave = async (svg: string) => {
  //   const { data, error } = await callHTTP('/api/v1/uploadSVG', {
  //     method: 'POST',
  //     body: { svg },
  //   });
  //   if (error) {
  //     throw new Error(error.message || '获取上传链接失败');
  //   }
  //   if (data) {
  //     const src = data.image_url;
  //     updateAttributes({ src });
  //   }
  // };

  return (
    <NodeViewWrapper
      contentEditable={false}
      className={cn(
        'group relative my-4',
        props.node.attrs[DIFF_CHANGE_ATTR_NAME] === DIFF_CHANGE_TYPE.REMOVED ? 'opacity-50' : '',
      )}
    >
      {/* <div
        data-drag-handle
        ref={imageContainerRef}
        className={`node-image-container relative flex max-w-full items-center justify-center rounded-lg border-2 ${
          isDragging ? `dragging-${dragSide}` : ""}`}
        style={{
          width: imageDisplayWidth ? imageDisplayWidth : "100%",
        }}
      >
        <div
          className="left-drag-line"
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDragImageSize(true, e);
          }}
        />
        <div
          className="right-drag-line"
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDragImageSize(false, e);
          }}
        /> */}
      <SVGEditor
        src={src}
        alt={alt}
        editEnabled={editor.isEditable}
        insertToThoughtEnabled={false}
        saveSnipEnabled={false}
        className="overflow-hidden w-full h-full rounded-lg"
        // onSave={handleSave}
      />
      {/* </div> */}
    </NodeViewWrapper>
  );
};
