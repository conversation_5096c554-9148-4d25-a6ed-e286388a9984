import type { NodeViewProps } from '@tiptap/react';
import { Ban } from 'lucide-react';

export const ImageFailedComponent = (props: NodeViewProps) => {
  const { width, height } = props.node.attrs;

  return (
    <div
      data-drag-handle
      className="relative flex h-[120px] w-full flex-col overflow-hidden rounded-lg border-2"
      style={{
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : '120px',
      }}
    >
      <div className="flex absolute flex-col gap-3 justify-center items-center w-full h-full text-disabled">
        <Ban />
        <div>Failed to load image</div>
      </div>
    </div>
  );
};
