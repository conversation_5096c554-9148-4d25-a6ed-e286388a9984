import { Image as ImageBase, type ImageExtensionOptions, ImageStatus } from '@repo/editor-common';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { ImageNodeView } from './image-node-view';
import { ImageUploadController } from './image-upload-controller';
export type { ImageExtensionOptions };
export { uploadImages } from './upload-Images';

interface ImageTemStoreInfo {
  dataUrl: string;
  file: File;
}

export const Image = ImageBase.extend<ImageExtensionOptions>({
  // 由于 tiptap 编辑器内部视图更新实现部没有把 diff 做好，所以会有很多 reRender 的情况，触发条件也比较苛刻，所以不要做很 hack 的逻辑来适配
  addNodeView() {
    return ReactNodeViewRenderer(ImageNodeView, {
      update: ({ oldNode, newNode }) => {
        // 因为内部图片渲染有 bug，所以这里只判断图片的 imageStatus 是否变化，来决定是否重新渲染
        // 要根本解决问题的话需要基于自研编辑器，不要再折腾了
        if (
          oldNode.attrs.imageStatus === ImageStatus.UPLOAD_IMAGE &&
          newNode.attrs.imageStatus === ImageStatus.LOADING
        ) {
          return true;
        }
        if (newNode.attrs.imageStatus === ImageStatus.FAILED) {
          return true;
        }
        if (oldNode.attrs.imageStatus === ImageStatus.LOADING) {
          return true;
        }
        return false;
      },
    });
  },

  addStorage() {
    return {
      uploadingImageMap: new Map<string, ImageTemStoreInfo>(),
      imageUploadController: new ImageUploadController(),
    };
  },
});
