import {
  ImageStatus,
  MAX_IMAGE_HEIGHT,
  MIN_IMAGE_HEIGHT,
  MIN_IMAGE_WIDTH,
} from '@repo/editor-common';
import type { Editor } from '@tiptap/react';

export const calculateImageDisplaySize = (width: number, height: number) => {
  // 添加参数验证和保障逻辑
  if (
    !width ||
    !height ||
    width <= 0 ||
    height <= 0 ||
    Number.isNaN(width) ||
    Number.isNaN(height)
  ) {
    // 返回合理的默认尺寸，避免图片变成小圆点
    return {
      imageDisplayWidth: Math.max(MIN_IMAGE_WIDTH, 400), // 默认宽度 400px
      imageDisplayHeight: Math.max(MIN_IMAGE_HEIGHT, 300), // 默认高度 300px
    };
  }

  // 确保尺寸不会小于最小值
  const validWidth = Math.max(width, MIN_IMAGE_WIDTH);
  const validHeight = Math.max(height, MIN_IMAGE_HEIGHT);

  if (validHeight <= MAX_IMAGE_HEIGHT) {
    return {
      imageDisplayWidth: validWidth,
      imageDisplayHeight: validHeight,
    };
  }

  const aspectRatio = validWidth / validHeight;
  const newWidth = Math.max(aspectRatio * MAX_IMAGE_HEIGHT, MIN_IMAGE_WIDTH);

  return {
    imageDisplayWidth: newWidth,
    imageDisplayHeight: MAX_IMAGE_HEIGHT,
  };
};

export const getCurImageList = (editor: Editor) => {
  if (!editor || !editor.state) {
    return [];
  }

  const imageList: Array<{ id: string; src: string; alt: string }> = [];
  const { doc } = editor.state;

  doc.nodesBetween(0, doc.content.size, (node) => {
    if (node.type.name === 'image') {
      const { id, src, alt, status } = node.attrs;

      if (
        id &&
        src &&
        src.trim() !== '' &&
        status !== ImageStatus.FAILED &&
        status !== ImageStatus.UPLOAD_IMAGE
      ) {
        imageList.push({
          id,
          src,
          alt: alt || '',
        });
      }
    }

    return true;
  });

  return imageList;
};

export function isBase64Image(imageUrl: string): boolean {
  return imageUrl.startsWith('data:');
}

export function isExternalImage(imageUrl: string): boolean {
  try {
    const currentDomain = window.location.hostname;
    const imageUrlObj = new URL(imageUrl);

    // cdn.gooo.ai 域名下的图片不算 external
    if (imageUrlObj.hostname === 'cdn.gooo.ai') {
      return false;
    }

    return imageUrlObj.hostname !== currentDomain;
  } catch (error) {
    return false;
  }
}

export type TransferFileRes =
  | {
      file: File;
      success: true;
    }
  | {
      success: false;
      file: null;
    };

export async function getFileFromImageUrl(
  imageUrl: string,
  fileName: string = 'image',
): Promise<TransferFileRes> {
  try {
    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const blob = await response.blob();

    // 从 URL 或 content-type 推断文件扩展名
    let fileExtension = '';
    const contentType = response.headers.get('content-type');

    if (contentType) {
      if (contentType.includes('jpeg') || contentType.includes('jpg')) {
        fileExtension = '.jpg';
      } else if (contentType.includes('png')) {
        fileExtension = '.png';
      } else if (contentType.includes('gif')) {
        fileExtension = '.gif';
      } else if (contentType.includes('webp')) {
        fileExtension = '.webp';
      }
    }

    // 如果没有扩展名，尝试从 URL 中提取
    if (!fileExtension) {
      const urlPath = new URL(imageUrl).pathname;
      const lastDot = urlPath.lastIndexOf('.');
      if (lastDot !== -1) {
        fileExtension = urlPath.slice(lastDot);
      }
    }

    const finalFileName = fileName.includes('.') ? fileName : `${fileName}${fileExtension}`;

    return {
      file: new File([blob], finalFileName, {
        type: blob.type || 'image/jpeg',
      }),
      success: true,
    };
  } catch (error) {
    return {
      file: null,
      success: false,
    };
  }
}

export function getFileFromBase64(
  base64String: string,
  fileName: string = 'image',
): TransferFileRes {
  try {
    let base64Data = base64String;
    let mimeType = 'image/jpeg'; // 默认类型

    // 如果是完整的 data URI，解析出 MIME 类型和纯 base64 数据
    if (base64String.startsWith('data:')) {
      const [header, data] = base64String.split(',');
      base64Data = data || '';

      // 提取 MIME 类型
      const mimeMatch = header?.match(/data:([^;]+)/);
      if (mimeMatch) {
        mimeType = mimeMatch[1] || '';
      }
    }

    // 将 base64 转换为二进制数据
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // 根据 MIME 类型确定文件扩展名
    let fileExtension = '';
    if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
      fileExtension = '.jpg';
    } else if (mimeType.includes('png')) {
      fileExtension = '.png';
    } else if (mimeType.includes('gif')) {
      fileExtension = '.gif';
    } else if (mimeType.includes('webp')) {
      fileExtension = '.webp';
    } else {
      fileExtension = '.jpg'; // 默认扩展名
    }

    const finalFileName = fileName.includes('.') ? fileName : `${fileName}${fileExtension}`;

    return {
      file: new File([bytes], finalFileName, {
        type: mimeType,
      }),
      success: true,
    };
  } catch (error) {
    return {
      file: null,
      success: false,
    };
  }
}
