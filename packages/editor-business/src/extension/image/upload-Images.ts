import { generateImageUniqueId, ImageStatus } from '@repo/editor-common';
import type { Editor } from '@tiptap/core';
import { calculateImageDisplaySize } from './utils';

// 添加文件大小限制和兼容性常量
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 50MB
const TIMEOUT_DURATION = 10000; // 10秒超时

// 处理后的图片数据接口
interface ProcessedImage {
  id: string;
  dataUrl: string;
  file: File;
  width: number;
  height: number;
  status: ImageStatus;
  displayWidth: number;
  displayHeight: number;
}

// 插入内容节点类型
interface InsertContentNode {
  type: string;
  attrs?: {
    src?: string;
    id?: string;
    status?: ImageStatus;
    alt?: string;
    width?: number;
    height?: number;
  };
}

// 检查文件是否为图片的更兼容的方法
function isImageFile(file: File): boolean {
  // 首先检查 MIME 类型
  if (file.type && file.type.startsWith('image/')) {
    return true;
  }

  // 如果 MIME 类型不可用，检查文件扩展名
  if (!file.type || file.type === '') {
    const fileName = file.name.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    return imageExtensions.some((ext) => fileName.endsWith(ext));
  }

  return false;
}

/**
 * 验证文件是否符合要求
 */
function validateFile(file: File): { valid: boolean; reason?: string } {
  if (!isImageFile(file)) {
    return {
      valid: false,
      reason: `跳过非图片文件: ${file.name}, type: ${file.type}`,
    };
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      reason: `文件大小超过限制: ${file.name}, 大小: ${file.size} bytes`,
    };
  }

  return { valid: true };
}

/**
 * 生成图片ID
 */
function generateImageId(): string {
  try {
    // 使用备用方案生成 ID，提高兼容性
    if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
      return generateImageUniqueId();
    } else {
      // 备用方案：使用时间戳和随机数
      return `imageId-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  } catch (error) {
    console.warn('生成ID失败，使用备用方案', error);
    return `imageId-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 读取文件为 dataURL
 */
function readFileAsDataURL(file: File): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    // 检查 FileReader 支持
    if (!window.FileReader) {
      reject(new Error('浏览器不支持 FileReader'));
      return;
    }

    const reader = new FileReader();
    let timeoutId: NodeJS.Timeout | null = null;

    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      // 移除事件监听器
      reader.onload = null;
      reader.onerror = null;
      reader.onabort = null;
    };

    // 设置超时
    timeoutId = setTimeout(() => {
      cleanup();
      try {
        reader.abort();
      } catch (e) {
        // 忽略 abort 错误
      }
      reject(new Error('文件读取超时'));
    }, TIMEOUT_DURATION);

    reader.onload = () => {
      cleanup();
      if (typeof reader.result === 'string') {
        // 验证 dataURL 格式
        if (reader.result.startsWith('data:image/')) {
          resolve(reader.result);
        } else {
          reject(new Error('无效的图片数据格式'));
        }
      } else {
        reject(new Error('读取结果不是字符串'));
      }
    };

    reader.onerror = () => {
      cleanup();
      const error = reader.error;
      reject(new Error(`文件读取失败: ${error?.message || 'unknown error'}`));
    };

    reader.onabort = () => {
      cleanup();
      reject(new Error('文件读取被中断'));
    };

    try {
      reader.readAsDataURL(file);
    } catch (error) {
      cleanup();
      reject(new Error(`启动文件读取失败: ${(error as Error).message}`));
    }
  });
}

/**
 * 获取图片尺寸
 */
function getImageDimensions(
  dataUrl: string,
  fileName: string,
): Promise<{
  width: number;
  height: number;
  failed: boolean;
}> {
  return new Promise<{ width: number; height: number; failed: boolean }>((resolve) => {
    const img = new Image();
    let timeoutId: NodeJS.Timeout | null = null;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      img.onload = null;
      img.onerror = null;
    };

    img.onload = () => {
      cleanup();
      // 添加尺寸验证
      const width = img.naturalWidth || img.width;
      const height = img.naturalHeight || img.height;

      if (width > 0 && height > 0) {
        console.log(`图片尺寸获取成功: ${fileName}, 尺寸: ${width}x${height}`);
        resolve({ width, height, failed: false });
      } else {
        // 尺寸无效，标记为失败
        console.warn(`图片尺寸无效: ${fileName}, width: ${width}, height: ${height}`);
        resolve({ width: 0, height: 0, failed: true });
      }
    };

    img.onerror = (error) => {
      cleanup();
      console.error(`图片加载失败: ${fileName}`, error);
      // 图片加载失败，标记为失败
      resolve({ width: 0, height: 0, failed: true });
    };

    // 设置超时，防止图片加载卡住
    timeoutId = setTimeout(() => {
      cleanup();
      console.warn(`图片加载超时: ${fileName}`);
      // 超时，标记为失败
      resolve({ width: 0, height: 0, failed: true });
    }, 5000); // 5秒超时

    try {
      img.src = dataUrl;
    } catch (error) {
      cleanup();
      console.error(`设置图片源失败: ${fileName}`, error);
      resolve({ width: 0, height: 0, failed: true });
    }
  });
}

/**
 * 处理单个文件
 */
async function processFile(file: File): Promise<ProcessedImage | null> {
  // 验证文件
  const validation = validateFile(file);
  if (!validation.valid) {
    console.warn(validation.reason);
    return null;
  }

  const imageId = generateImageId();

  try {
    // 读取文件
    const dataUrl = await readFileAsDataURL(file);

    // 获取图片尺寸
    const { width, height, failed } = await getImageDimensions(dataUrl, file.name);

    let imageDisplayWidth, imageDisplayHeight, status;

    if (failed) {
      // 尺寸获取失败，设置为失败状态
      status = ImageStatus.FAILED;
      const fallbackSize = calculateImageDisplaySize(400, 300);
      imageDisplayWidth = fallbackSize.imageDisplayWidth;
      imageDisplayHeight = fallbackSize.imageDisplayHeight;
    } else {
      status = ImageStatus.UPLOAD_IMAGE;
      const size = calculateImageDisplaySize(width, height);
      imageDisplayWidth = size.imageDisplayWidth;
      imageDisplayHeight = size.imageDisplayHeight;
    }

    return {
      id: imageId,
      dataUrl,
      file,
      width,
      height,
      status,
      displayWidth: imageDisplayWidth,
      displayHeight: imageDisplayHeight,
    };
  } catch (error) {
    console.error(`文件处理失败: ${file.name}`, error);
    return null;
  }
}

/**
 * 批量插入图片到编辑器
 */
function insertImages(editor: Editor, images: ProcessedImage[], startPos: number): void {
  // 确保 editor.storage.image 存在
  if (!editor.storage.image) {
    editor.storage.image = { uploadingImageMap: new Map() };
  }
  if (!editor.storage.image.uploadingImageMap) {
    editor.storage.image.uploadingImageMap = new Map();
  }

  // 准备插入的内容
  const insertContent: InsertContentNode[] = [];
  const successCount = 0;

  images.forEach((image, index) => {
    // 添加到上传映射
    editor.storage.image.uploadingImageMap.set(image.id, {
      dataUrl: image.dataUrl,
      file: image.file,
    });

    // 创建图片节点
    const imageNode = {
      type: 'image',
      attrs: {
        src: '',
        id: image.id,
        status: image.status,
        alt: image.file.name,
        width: image.displayWidth,
        height: image.displayHeight,
      },
    };

    insertContent.push(imageNode);
  });

  // 确保插入位置不超出文档范围
  const maxPos = editor.state.doc.content.size;
  const insertPos = Math.min(startPos, maxPos);

  try {
    editor.commands.insertContentAt(insertPos, insertContent);
  } catch (error) {
    // ignore
  }
}

/**
 * 主函数：上传图片
 */
export async function uploadImages(editor: Editor, files: File[], pos: number) {
  if (!files || files.length === 0) {
    return;
  }

  try {
    // 并行处理所有文件
    const processedImages = await Promise.all(files.map((file) => processFile(file)));

    // 过滤掉处理失败的文件
    const validImages = processedImages.filter((image): image is ProcessedImage => image !== null);

    if (validImages.length === 0) {
      return;
    }

    // 批量插入所有图片
    insertImages(editor, validImages, pos);
  } catch (error) {
    console.error('Fail to upload images', error);
  }
}
