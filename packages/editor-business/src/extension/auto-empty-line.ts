import { Extension } from '@tiptap/core';
import { Plugin } from '@tiptap/pm/state';

export const AutoEmptyLineName = 'autoEmptyLine';

export const AutoEmptyLine = Extension.create({
  name: AutoEmptyLineName,

  addProseMirrorPlugins() {
    return [
      new Plugin({
        appendTransaction: (_, oldState, newState) => {
          const { doc, tr } = newState;
          // 检查文档最后一个节点是否是空段落
          const lastNode = doc.lastChild;

          if (lastNode?.type.name === 'paragraph' && lastNode.content.size === 0) {
            return null;
          }

          // 插入新空段落
          const paragraph = newState.schema.nodes.paragraph?.create();
          if (paragraph) {
            return tr.insert(doc.content.size, paragraph);
          }

          return tr;
        },
      }),
    ];
  },
});
