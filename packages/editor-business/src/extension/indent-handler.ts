import { Extension } from '@tiptap/core';

const TAB_CHAR = '\u0009';

/**
 * @see https://gist.github.com/rchasman/1504332a3b50eb4aa616f1d9341d4001
 */
export const IndentHandler = Extension.create({
  name: 'indentHandler',
  addKeyboardShortcuts() {
    return {
      Tab: ({ editor }) => {
        if (editor.isActive('listItem')) {
          const sinkResult = editor.chain().sinkListItem('listItem').run();
          if (sinkResult) {
            return true;
          }
        }

        if (editor.isActive('taskItem')) {
          const sinkResult = editor.chain().sinkListItem('taskItem').run();
          if (sinkResult) {
            return true;
          }
        }

        if (editor.isActive('codeBlock')) {
          // Insert a tab character
          editor
            .chain()
            .command(({ tr }) => {
              tr.insertText(TAB_CHAR);
              return true;
            })
            .run();
        }
        // Prevent default behavior (losing focus)
        return true;
      },
      'Shift-Tab': ({ editor }) => {
        const { selection, doc } = editor.state;
        const { $from } = selection;
        const pos = $from.pos;

        if (editor.isActive('listItem')) {
          return editor.chain().liftListItem('listItem').run();
        }

        if (editor.isActive('taskItem')) {
          return editor.chain().liftListItem('taskItem').run();
        }

        if (editor.isActive('codeBlock')) {
          // Check if the previous character is a tab
          if (doc.textBetween(pos - 1, pos) === TAB_CHAR) {
            // If so, delete it
            editor
              .chain()
              .command(({ tr }) => {
                tr.delete(pos - 1, pos);
                return true;
              })
              .run();
            return true;
          }
        }

        // Prevent default behavior (losing focus)
        return true;
      },
    };
  },
});
