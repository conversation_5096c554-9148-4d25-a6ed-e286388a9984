import { Paragraph } from '@repo/editor-common';
import { type Editor, Extension } from '@tiptap/core';

export interface BackspaceNodeConfig {
  /** 节点类型名称 */
  nodeType: string;
  /** 是否检查当前光标是否在节点内部（默认 true） */
  checkInside?: boolean;
}

export interface NavigationNodeConfig {
  /** 节点类型名称 */
  nodeType: string;
}

export interface NodeNavigationAdapterOptions {
  /** 需要处理的节点类型配置 */
  nodeConfigs: BackspaceNodeConfig[];
  /** 需要处理导航的节点类型配置 */
  navigationNodeConfigs: NavigationNodeConfig[];
}

export const NodeNavigationAdapterName = 'NodeNavigationAdapter';

export const NodeNavigationAdapter = Extension.create<NodeNavigationAdapterOptions>({
  name: NodeNavigationAdapterName,
  priority: 1000,

  addOptions() {
    return {
      nodeConfigs: [],
      navigationNodeConfigs: [],
    };
  },

  addKeyboardShortcuts() {
    // 处理导航逻辑（ArrowUp 和 ArrowLeft）
    const handleNavigationFromStart = ({ editor }: { editor: Editor }) => {
      const { state } = editor;
      const { selection } = state;
      const { $from } = selection;

      // 检查是否在配置的节点类型内
      const currentNodeType = $from.parent.type.name;
      const isTargetNode = this.options.navigationNodeConfigs.some(
        (config) => config.nodeType === currentNodeType,
      );

      if (!isTargetNode) {
        return false;
      }

      // 检查是否在节点的第一位置
      const isAtStart = $from.parentOffset === 0;
      if (!isAtStart) {
        return false;
      }

      // 获取当前节点的位置
      const nodePos = $from.before();

      // 检查节点前面是否有节点
      const $beforePos = state.doc.resolve(nodePos);
      const prevNode = $beforePos.nodeBefore;

      if (prevNode) {
        // 检查前面的节点是否为空段落
        const isPrevNodeEmptyParagraph =
          prevNode.type.name === 'paragraph' && prevNode.content.size === 0;

        if (isPrevNodeEmptyParagraph) {
          // 如果前面是空段落，将光标移动到那里
          const prevNodeEnd = nodePos - 1;
          editor.commands.focus(prevNodeEnd);
        } else {
          // 如果前面的节点不是空段落，插入一个空段落并将光标移动到那里
          editor
            .chain()
            .insertContentAt(nodePos, {
              type: Paragraph.name,
            })
            .focus(nodePos + 1)
            .run();
        }
      } else {
        // 如果前面没有节点，插入一个空段落并将光标移动到那里
        editor
          .chain()
          .insertContentAt(nodePos, {
            type: Paragraph.name,
          })
          .focus(nodePos + 1)
          .run();
      }

      return true;
    };

    return {
      Backspace: ({ editor }) => {
        const { selection } = editor.state;
        const { $from, $to } = selection;

        // 如果是选区则不进行处理
        if ($from.pos !== $to.pos) {
          return false;
        }

        // 第一阶段：优先处理当前节点内部的逻辑
        for (const config of this.options.nodeConfigs) {
          if (config.checkInside !== false && $from.parent.type.name === config.nodeType) {
            // 如果在节点内部且光标在开头，选中整个节点
            if ($from.parentOffset === 0) {
              const nodePos = $from.before();
              editor.commands.setNodeSelection(nodePos);
              return true;
            }
            // 如果在节点内部但不在开头，不处理
            return false;
          }
        }

        // 第二阶段：处理前一个节点的逻辑
        for (const config of this.options.nodeConfigs) {
          // 如果光标不在段落开头则不处理
          if ($from.parentOffset !== 0) {
            continue;
          }

          // 如果当前段落不为空则不处理（针对简化模式）
          if (config.checkInside === false && $from.parent.content.size !== 0) {
            continue;
          }

          // 检查前一个节点是否是指定节点类型
          const nodeBefore =
            $from.before() > 0 ? editor.state.doc.resolve($from.before()).nodeBefore : null;

          if (nodeBefore && nodeBefore.type.name === config.nodeType) {
            if (config.checkInside !== false) {
              // 完整模式：检查当前段落是否为空
              const currentNode = $from.parent;
              const isEmpty = currentNode.content.size === 0;

              if (isEmpty) {
                // 删除当前空段落
                editor.commands.deleteRange({
                  from: $from.before(),
                  to: $from.pos,
                });
              }

              // 选中整个节点
              const nodePos = $from.before() - nodeBefore.nodeSize;
              editor.commands.setNodeSelection(nodePos);
            } else {
              // 简化模式：直接处理
              const nodePos = $from.before() - 1;

              editor.commands.deleteRange({
                from: $from.before(),
                to: $from.pos,
              });

              editor.commands.setNodeSelection(nodePos);
            }

            return true;
          }
        }

        // 如果没有任何节点类型处理 backspace，返回 false
        return false;
      },
      ArrowUp: handleNavigationFromStart,
      ArrowLeft: handleNavigationFromStart,
    };
  },
});
