import { Bullet<PERSON>ist, OrderedList, Paragraph, TaskList } from '@repo/editor-common';
import type { Editor } from '@tiptap/react';
import {
  CodeXml,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  ListTodo,
  Minus,
  Table,
  Type,
} from 'lucide-react';
import { ThoughtImageIcon } from '../../icon/thought-image-icon';
import { ThoughtMermaidIcon } from '../../icon/thought-mermaid-icon';
import { ThoughtQuoteIcon } from '../../icon/thought-quote-icon';
import { uploadImages } from '../image/upload-Images';
import { COMMAND_ALIASES } from './aliases';

export const EXTENSION_NAME = 'slashCommand';

// 辅助函数：检查当前节点是否为空
export const isCurrentNodeEmpty = (editor: Editor) => {
  const { state } = editor;
  const { selection } = state;
  const { $from } = selection;

  // 检查当前段落是否为空
  const currentNode = $from.parent;
  return currentNode.textContent.trim().length === 0;
};

export enum GROUP_NAME {
  FORMAT = 'format',
}

export interface IMenuItem {
  name: string;
  label: string;
  icon?: React.ReactNode;
  description: string;
  markdownTip?: string;
  aliases: string[];
  action: (editor: Editor) => void;
}

export const MENU_ITEMS: IMenuItem[] = [
  {
    name: 'heading1',
    label: 'Title',
    icon: <Heading1 size={16} />,
    description: 'High priority section title',
    markdownTip: '#',
    aliases: COMMAND_ALIASES.heading1!,
    action: (editor) => {
      editor.chain().focus().setHeading({ level: 1 }).scrollCursorIntoView().run();
    },
  },
  {
    name: 'heading2',
    label: 'Subtitle',
    icon: <Heading2 size={16} />,
    description: 'Medium priority section title',
    aliases: COMMAND_ALIASES.heading2!,
    markdownTip: '##',
    action: (editor) => {
      editor.chain().focus().setHeading({ level: 2 }).scrollCursorIntoView().run();
    },
  },
  {
    name: 'heading3',
    label: 'Heading',
    icon: <Heading3 size={16} />,
    description: 'Low priority section title',
    aliases: COMMAND_ALIASES.heading3!,
    markdownTip: '###',
    action: (editor) => {
      editor.chain().focus().setHeading({ level: 3 }).scrollCursorIntoView().run();
    },
  },
  {
    name: 'text',
    label: 'Text',
    icon: <Type size={14} />,
    description: 'Text',
    aliases: COMMAND_ALIASES.text!,
    action: (editor) => {
      editor.chain().focus().setParagraph().scrollCursorIntoView().run();
    },
  },
  {
    name: 'taskList',
    label: 'Task list',
    icon: <ListTodo size={16} />,
    description: 'Task list with todo items',
    aliases: COMMAND_ALIASES.taskList!,
    markdownTip: '[]',
    action: (editor) => {
      // 如果已经是任务列表，则不执行操作
      if (editor.isActive(TaskList.name)) {
        return;
      }
      editor.chain().focus().toggleTaskList().scrollCursorIntoView().run();
    },
  },
  {
    name: 'orderedList',
    label: 'Numbered list',
    icon: <ListOrdered size={16} />,
    description: 'Ordered list of items',
    aliases: COMMAND_ALIASES.orderedList!,
    markdownTip: '1.',
    action: (editor) => {
      // 如果已经是有序列表，则不执行操作
      if (editor.isActive(OrderedList.name)) {
        return;
      }
      editor.chain().focus().toggleOrderedList().scrollCursorIntoView().run();
    },
  },
  {
    name: 'bulletList',
    label: 'Bullet list',
    icon: <List size={16} />,
    description: 'Unordered list of items',
    aliases: COMMAND_ALIASES.bulletList!,
    markdownTip: '-',
    action: (editor) => {
      // 如果已经是无序列表，则不执行操作
      if (editor.isActive(BulletList.name)) {
        return;
      }
      editor.chain().focus().toggleBulletList().scrollCursorIntoView().run();
    },
  },
  {
    name: 'blockquote',
    label: 'Quote',
    icon: <ThoughtQuoteIcon size={16} />,
    description: 'Element for quoting',
    markdownTip: '>',
    aliases: COMMAND_ALIASES.blockquote!,
    action: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setBlockquote()
        .scrollCursorIntoView()
        .run();
    },
  },
  {
    name: 'codeBlock',
    label: 'Code',
    icon: <CodeXml size={16} />,
    description: 'Insert a code block',
    markdownTip: '```',
    aliases: COMMAND_ALIASES.codeBlock!,
    action: (editor) => {
      if (isCurrentNodeEmpty(editor)) {
        editor.chain().focus().setCodeBlock().scrollCursorIntoView().run();
      } else {
        editor
          .chain()
          .focus()
          .insertContentAt(editor.state.selection.to, {
            type: Paragraph.name,
          })
          .setCodeBlock()
          .scrollCursorIntoView()
          .run();
      }
    },
  },
  {
    name: 'image',
    label: 'Image',
    icon: <ThoughtImageIcon size={16} />,
    description: 'Insert an image',
    aliases: COMMAND_ALIASES.image!,
    action: (editor) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = true;
      input.onchange = async () => {
        const files = Array.from(input.files || []);
        if (files.length === 0) return;

        const view = editor.view;
        uploadImages(editor, files, view.state.selection.from);
      };
      input.click();
    },
  },
  {
    name: 'table',
    label: 'Table',
    icon: <Table size={16} />,
    description: 'Insert a table',
    aliases: COMMAND_ALIASES.table!,
    action: (editor) => {
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: false })
        .scrollCursorIntoView()
        .run();
    },
  },
  {
    name: 'horizontalRule',
    label: 'Divider',
    icon: <Minus size={16} />,
    description: 'Insert a horizontal divider',
    markdownTip: '---',
    aliases: COMMAND_ALIASES.horizontalRule!,
    action: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setHorizontalRule()
        .scrollCursorIntoView()
        .run();
    },
  },

  {
    name: 'mermaid',
    label: 'Text diagram',
    icon: <ThoughtMermaidIcon size={16} />,
    description: 'Insert a mermaid diagram',
    aliases: COMMAND_ALIASES.mermaid!,
    action: (editor) => {
      if (isCurrentNodeEmpty(editor)) {
        editor.chain().focus().setMermaid().scrollCursorIntoView().run();
      } else {
        editor
          .chain()
          .focus()
          .insertContentAt(editor.state.selection.to, {
            type: Paragraph.name,
          })
          .setMermaid()
          .scrollCursorIntoView()
          .run();
      }
    },
  },
];

// if (isDev() || isPreview()) {
//   GROUPS.find((group) => group.name === GROUP_NAME.FORMAT)?.commands.push({
//     name: "drawingBoard",
//     label: "Drawing board",
//     iconName: "Presentation",
//     description: "Insert an drawing board",
//     action: (editor) => {
//       editor.chain().focus().addDrawingBoard().run();
//     },
//   });
// }
