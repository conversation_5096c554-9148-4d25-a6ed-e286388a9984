import {
  Blockquote,
  BulletList,
  Document,
  ListItem,
  OrderedList,
  Paragraph,
  TaskItem,
  TaskList,
} from '@repo/editor-common';
import type { Editor } from '@tiptap/core';
import { PluginKey } from '@tiptap/pm/state';
import { <PERSON>act<PERSON>enderer } from '@tiptap/react';
import tippy from 'tippy.js';
import {
  Suggestion,
  type SuggestionKeyDownProps,
  type SuggestionProps,
} from '../suggestion/suggestion';
import { EXTENSION_NAME, MENU_ITEMS } from './constant';
import { MenuList } from './menu-list';

export interface SlashCommandOptions {
  decorationClass: string;
}

const ALLOW_SLASH_COMMAND_NODE_TYPES = [
  Paragraph.name,
  BulletList.name,
  OrderedList.name,
  Blockquote.name,
  TaskList.name,
  TaskItem.name,
  ListItem.name,
  Document.name,
];

export function genSuggestion(editor: Editor, options: SlashCommandOptions) {
  // const updateEventHandler = new EventHandler<IUpdateEvent>();
  // const keyDownEventHandler = new EventHandler<IKeydownEvent>();

  return Suggestion({
    editor,
    char: '/',
    allowSpaces: false,
    startOfLine: false,
    pluginKey: new PluginKey(EXTENSION_NAME),
    decorationClass: options.decorationClass,
    allow: ({ state, range }) => {
      const $from = state.doc.resolve(range.from);
      const currentNodeType = $from.parent.type.name;

      // 检查当前节点类型是否在允许的类型列表中
      const isAllowedNodeType = ALLOW_SLASH_COMMAND_NODE_TYPES.includes(currentNodeType);

      // 如果当前节点是段落，还需要检查父节点是否也在允许的类型中
      if (currentNodeType === Paragraph.name) {
        // 使用 depth - 1 来获取父级 resolved position
        const parentDepth = $from.depth - 1;
        const parentNodeType = parentDepth >= 0 ? $from.node(parentDepth).type.name : null;
        const isParentAllowed = parentNodeType
          ? ALLOW_SLASH_COMMAND_NODE_TYPES.includes(parentNodeType)
          : false;

        return isAllowedNodeType && isParentAllowed;
      }
      // tiptap 已经通过 char: "/" 检测了斜杠字符，这里只需要检查节点类型即可
      return isAllowedNodeType;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    command: ({ editor, props }: { editor: Editor; props: any }) => {
      const { view, state } = editor;
      const { $head, $from } = view.state.selection;
      const end = $from.pos;
      const from = $head?.nodeBefore
        ? end - ($head.nodeBefore.text?.substring($head.nodeBefore.text?.indexOf('/')).length ?? 0)
        : $from.start();

      const tr = state.tr.deleteRange(from, end);
      view.dispatch(tr);

      props.action(editor);
      view.focus();
    },
    items: ({ query }: { query: string }) => {
      const filteredItems = MENU_ITEMS.filter((item) => {
        const labelNormalized = item.label.toLowerCase().trim();
        const queryNormalized = query.toLowerCase().trim();

        // 如果查询为空，显示所有项目
        if (!queryNormalized) {
          return true;
        }

        // 模糊匹配函数：检查查询字符串的字符是否按顺序出现在目标字符串中
        const fuzzyMatch = (query: string, target: string): boolean => {
          let queryIndex = 0;
          for (let i = 0; i < target.length && queryIndex < query.length; i++) {
            if (target[i] === query[queryIndex]) {
              queryIndex++;
            }
          }
          return queryIndex === query.length;
        };

        // 检查标签匹配 - 使用多种匹配策略
        const labelMatches =
          labelNormalized.startsWith(queryNormalized) || // 前缀匹配
          labelNormalized.includes(queryNormalized) || // 完全包含匹配
          fuzzyMatch(queryNormalized, labelNormalized); // 模糊匹配

        // 检查别名匹配
        let aliasMatches = false;
        if (item.aliases) {
          aliasMatches = item.aliases.some((alias: string) => {
            const aliasNormalized = alias.toLowerCase().trim();
            return (
              aliasNormalized.startsWith(queryNormalized) ||
              aliasNormalized.includes(queryNormalized) ||
              fuzzyMatch(queryNormalized, aliasNormalized)
            );
          });
        }

        return labelMatches || aliasMatches;
      }).map((item) => ({
        ...item,
        isEnabled: true,
      }));

      return filteredItems;
    },
    render: () => {
      let component: ReactRenderer | null = null;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let popup: any = null;

      return {
        onStart: (props: SuggestionProps) => {
          component = new ReactRenderer(MenuList, {
            props: {
              ...props,
              onExit: () => {
                popup[0].hide();
                // 插入空格防止再次触发 slash 面板
                props.editor.commands.insertContent(' ');
              },
            },
            editor: props.editor,
          });

          if (!props.clientRect) {
            return;
          }
          component.element.classList.add('shadow-lg', 'rounded-xl');

          // @ts-expect-error tippy
          popup = tippy('body', {
            getReferenceClientRect: props.clientRect!,
            appendTo: () => editor.view.dom.parentElement,
            content: component?.element,
            showOnCreate: true,
            interactive: true,
            trigger: 'manual',
            placement: 'bottom-start',
            theme: 'slash-command',
            maxWidth: '16rem',
            popperOptions: {
              modifiers: [
                {
                  name: 'flip',
                  enabled: true,
                  options: {
                    fallbackPlacements: ['top-start'],
                    padding: 0,
                  },
                },
                {
                  name: 'offset',
                  options: {
                    offset: ({ placement }: { placement: string }) => {
                      return placement.startsWith('top') ? [8, -24] : [8, 8];
                    },
                  },
                },
                {
                  name: 'eventListeners',
                  enabled: true,
                  options: {
                    scroll: true, // 显式启用滚动监听
                    resize: true, // 启用窗口resize监听
                  },
                },
                {
                  name: 'preventOverflow',
                  enabled: true,
                  options: {
                    boundary: 'viewport',
                    padding: 24,
                  },
                },
              ],
            },
          });
        },

        onUpdate(props: SuggestionProps) {
          component?.updateProps({
            ...props,
            onExit: () => {
              popup[0].hide();
              // 插入空格防止再次触发 slash 面板
              props.editor.commands.insertContent(' ');
            },
          });

          if (!props.clientRect) {
            return;
          }
        },

        onKeyDown(props: SuggestionKeyDownProps) {
          if (props.event.key === 'Escape') {
            popup[0].hide();

            // 插入空格防止再次触发 slash 面板
            editor.commands.insertContent(' ');

            return true;
          }

          // 如果组件引用存在，处理键盘事件
          // 这里有个问题，debug 的 sourcemap 不对
          if (component?.element.isConnected) {
            const { event } = props;

            // 处理方向键
            if (
              event.key === 'ArrowUp' ||
              event.key === 'ArrowDown' ||
              event.key === 'ArrowLeft' ||
              event.key === 'ArrowRight'
            ) {
              // @ts-expect-error component ref
              return component.ref?.onKeyDown(props);
            }

            // 处理选择键
            if (event.key === 'Enter' || event.key === 'Tab') {
              // @ts-expect-error component ref
              const result = component.ref?.onKeyDown(props);

              if (result) {
                event.preventDefault();
              }

              return result;
            }
          }

          return false;
        },

        onExit() {
          popup?.[0]?.destroy();
          component?.destroy();
        },
      };
    },
  });
}
