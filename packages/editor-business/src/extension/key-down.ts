import { type Editor, Extension, type KeyboardShortcutCommand } from '@tiptap/core';

// 后面要加的 Key 都放到这里
export interface KeyDownOptions {
  Backspace?: KeyboardShortcutCommand;
  'Ctrl-k'?: KeyboardShortcutCommand;
}

export function isSelectionInSameNode(editor: Editor): boolean {
  const { state } = editor.view;
  const { $from, $to } = state.selection;

  // 检查是否是同一个节点
  if ($from.parent !== $to.parent) {
    return false;
  }

  return true;
}

export function isEntireNodeSelected(editor: Editor): boolean {
  const { state } = editor.view;
  const { $from, $to } = state.selection;

  // 如果不在同一个节点中，返回 false
  if (!isSelectionInSameNode(editor)) {
    return false;
  }

  // 检查选区是否覆盖整个节点
  return $from.parentOffset === 0 && $to.parentOffset === $to.parent.content.size;
}

export const KeyDownCustomExtensionName = 'KeydownExtensionCustom';

export const KeyDownCustomExtension = Extension.create<KeyDownOptions>({
  name: KeyDownCustomExtensionName,
  addKeyboardShortcuts() {
    return {
      Backspace: (params) => {
        return this.options.Backspace?.(params) ?? false;
      },
      // 重写 Mod-a 快捷键，使其对所有节点生效
      'Mod-a': ({ editor }) => {
        // 如果选区不在同一个节点内，返回 false，让默认行为生效
        if (!isSelectionInSameNode(editor)) {
          return false;
        }

        // 如果已经选中整个节点，返回 false，让默认行为生效（选中整个文档）
        if (isEntireNodeSelected(editor)) {
          return false;
        }

        // 获取当前节点的位置
        const { state } = editor.view;
        const { $from } = state.selection;
        const pos = $from.start();
        const node = $from.node();

        // 选中整个节点
        editor.commands.setTextSelection({
          from: pos,
          to: pos + node.content.size,
        });

        return true;
      },
      'Ctrl-k': (params) => {
        // 如果定义了自定义的 Ctrl-k 处理器，使用它
        if (this.options['Ctrl-k']) {
          return this.options['Ctrl-k'](params);
        }

        // 默认的 Ctrl-k 行为：删除至行尾
        const { editor } = params;
        const { state } = editor.view;
        const { selection } = state;
        const { $from } = selection;

        // 获取当前节点和位置信息
        const currentNode = $from.parent;
        const currentPos = $from.pos;
        const nodeStart = $from.start();

        // 计算当前光标在节点内的偏移
        const offsetInNode = $from.parentOffset;
        const nodeText = currentNode.textContent;

        // 找到当前行的结束位置
        let lineEndOffset = nodeText.length;
        const textAfterCursor = nodeText.slice(offsetInNode);
        const newlineIndex = textAfterCursor.indexOf('\n');

        if (newlineIndex !== -1) {
          // 如果找到换行符，行结束位置就是换行符之前
          lineEndOffset = offsetInNode + newlineIndex;
        }

        // 计算删除范围
        const deleteFrom = currentPos;
        const deleteTo = nodeStart + lineEndOffset;

        // 如果有内容需要删除
        if (deleteTo > deleteFrom) {
          editor.commands.deleteRange({ from: deleteFrom, to: deleteTo });
        }

        return true;
      },
    };
  },
});
