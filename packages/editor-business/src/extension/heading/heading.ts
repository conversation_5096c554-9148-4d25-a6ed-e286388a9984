import './heading.css';
import { DIFF_CHANGE_ATTR_NAME, Heading as HeadingBase, type Level } from '@repo/editor-common';
import { mergeAttributes } from '@tiptap/core';
import { STYLE_MAP } from './style-map';

export const Heading = HeadingBase.extend({
  renderHTML({ node, HTMLAttributes }) {
    const hasLevel = this.options.levels.includes(node.attrs.level);
    const level: Level = hasLevel ? node.attrs.level : this.options.levels[0];
    const styleClass = STYLE_MAP[level];

    // 确保类名正确合并
    const existingClass = HTMLAttributes?.class || '';
    const mergedClass = existingClass ? `${existingClass} ${styleClass}` : styleClass;

    return [
      `h${level}`,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        [DIFF_CHANGE_ATTR_NAME]: node.attrs[DIFF_CHANGE_ATTR_NAME],
        class: mergedClass,
      }),
      0,
    ];
  },
});
