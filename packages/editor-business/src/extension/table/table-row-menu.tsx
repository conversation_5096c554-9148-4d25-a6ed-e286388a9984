import { Button } from '@repo/ui/components/ui/button';
import { BubbleMenu } from '@tiptap/react';
import { ArrowDownToLine, ArrowUpToLine, Trash } from 'lucide-react';
import { memo, useCallback } from 'react';

import type { MenuProps, ShouldShowProps } from './type';
import { getTableRowsCount, isRowGripSelected } from './utils';

export const TableRowMenu = memo(({ editor, appendTo }: MenuProps) => {
  const shouldShow = useCallback(
    ({ view, state, from }: ShouldShowProps) => {
      if (!state || !from) {
        return false;
      }

      return isRowGripSelected({ editor, view, state, from });
    },
    [editor],
  );

  const onAddRowBefore = useCallback(() => {
    editor.chain().focus().addRowBefore().run();
  }, [editor]);

  const onAddRowAfter = useCallback(() => {
    editor.chain().focus().addRowAfter().run();
  }, [editor]);

  const onDeleteRow = useCallback(() => {
    const rowsCount = getTableRowsCount(editor.state.selection);
    if (rowsCount === 1) {
      // 如果只有一行，删除整个表格
      editor.chain().focus().deleteTable().run();
    } else {
      // 否则只删除当前行
      editor.chain().focus().deleteRow().run();
    }
  }, [editor]);

  return (
    <BubbleMenu
      editor={editor}
      pluginKey="tableRowMenu"
      shouldShow={shouldShow}
      updateDelay={0}
      tippyOptions={{
        appendTo: appendTo.current!,
        placement: 'left',
        offset: [0, 0],
        popperOptions: {
          modifiers: [{ name: 'flip', enabled: false }],
        },
      }}
    >
      <div className="inline-flex flex-col gap-0.5 rounded-lg bg-white p-1.5 shadow-lg">
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddRowBefore}
          variant="ghost"
        >
          <ArrowUpToLine className="mr-2" size={16} />
          Add row before
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onAddRowAfter}
          variant="ghost"
        >
          <ArrowDownToLine className="mr-2" size={16} />
          Add row after
        </Button>
        <Button
          className="justify-start p-1.5 text-left text-sm rounded-md"
          onClick={onDeleteRow}
          variant="ghost"
        >
          <Trash className="mr-2" size={16} />
          Delete row
        </Button>
      </div>
    </BubbleMenu>
  );
});

TableRowMenu.displayName = 'TableRowMenu';
