import type { ViewMode } from '@repo/editor-common';
import { DIFF_CHANGE_ATTR_NAME, DIFF_CHANGE_TYPE } from '@repo/editor-common';
import { cn } from '@repo/ui/lib/utils';
import { NodeViewContent, type NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Image as AntdImage } from 'antd';
import { Edit, Eye, Maximize2 } from 'lucide-react';
import type { MermaidConfig } from 'mermaid';
import mermaid from 'mermaid';
import { useCallback, useEffect, useRef, useState } from 'react';
import { escapeMermaidMarkdown } from './escapeMermaidMarkdown';

const SimpleLoading = (props: any) => {
  return <div>TODO</div>;
};

// mermaid 配置
const MERMAID_CONFIG: MermaidConfig = {
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose',
  suppressErrorRendering: true,
  // 添加尺寸相关配置
  flowchart: {
    useMaxWidth: false,
    htmlLabels: true,
  },
  sequence: {
    useMaxWidth: false,
  },
  gantt: {
    useMaxWidth: false,
  },
  journey: {
    useMaxWidth: false,
  },
  timeline: {
    useMaxWidth: false,
  },
} as const;

interface MermaidState {
  renderedSvg: string;
  error: string;
  isRendering: boolean;
}

// 初始化 mermaid
mermaid.initialize(MERMAID_CONFIG);

const { PreviewGroup } = AntdImage;

export const MermaidComponent = (props: NodeViewProps) => {
  const [state, setState] = useState<MermaidState>({
    renderedSvg: '',
    error: '',
    isRendering: false,
  });
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string>('');

  const codeRef = useRef<HTMLPreElement>(null);
  const previousCodeRef = useRef<string>('');

  // 获取当前视图模式 - 优先使用 options.mode，其次使用 attrs.mode
  const getCurrentViewMode = useCallback((): ViewMode => {
    const optionMode = props.extension.options.mode;
    if (optionMode) {
      return optionMode;
    }
    return props.node.attrs.mode || 'edit';
  }, [props.extension.options.mode, props.node.attrs.mode]);

  // 检查是否应该显示切换按钮 - 只有在 options.mode 为空时才显示
  const shouldShowToggle = !props.extension.options.mode;

  const currentViewMode = getCurrentViewMode();

  // 处理 SVG 尺寸的辅助函数
  const processSvgForDisplay = useCallback((svgString: string): string => {
    // 创建一个临时 DOM 元素来解析 SVG
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svgElement = doc.querySelector('svg');

    if (svgElement) {
      // 移除固定的宽高，保留 viewBox
      svgElement.removeAttribute('height');
      svgElement.removeAttribute('width');

      // 确保有 viewBox，如果没有则根据当前尺寸创建
      if (!svgElement.getAttribute('viewBox')) {
        const width = svgElement.getAttribute('width') || '800';
        const height = svgElement.getAttribute('height') || '600';
        svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
      }

      // 设置响应式属性
      // <svg> 元素的 width 属性期望的是一个合法的长度（如 100px、50%）或数字，不支持 auto。
      // 否则截图时调用 cloneNode 会报错
      svgElement.setAttribute('width', '100%');
      svgElement.setAttribute('height', '100%');

      // 添加 CSS 类而不是内联样式，限制最大高度为 500px
      svgElement.setAttribute('class', 'h-auto max-w-full max-h-[500px]');

      return new XMLSerializer().serializeToString(svgElement);
    }

    return svgString;
  }, []);

  // 将 SVG 转换为 data URL 用于预览
  const convertSvgToDataUrl = useCallback((svgString: string): string => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svgElement = doc.querySelector('svg');

    if (svgElement) {
      // 为预览优化 SVG - 移除限制类，保持原始尺寸
      const clonedSvg = svgElement.cloneNode(true) as SVGElement;
      clonedSvg.removeAttribute('class');
      clonedSvg.setAttribute('width', 'auto');
      clonedSvg.setAttribute('height', 'auto');

      const serializedSvg = new XMLSerializer().serializeToString(clonedSvg);
      const encodedSvg = encodeURIComponent(serializedSvg);
      return `data:image/svg+xml,${encodedSvg}`;
    }

    return '';
  }, []);

  // 渲染 mermaid 图表
  const renderMermaid = useCallback(
    async (code: string) => {
      if (!code.trim()) {
        setState((prev) => ({
          ...prev,
          renderedSvg: '',
          error: '',
          isRendering: false,
        }));
        setPreviewImageUrl('');
        return;
      }

      // 设置渲染中状态
      setState((prev) => ({
        ...prev,
        isRendering: true,
      }));

      try {
        const escapedCode = escapeMermaidMarkdown(code);
        const { svg } = await mermaid.render(`mermaid-${Date.now()}`, escapedCode);

        // 处理 SVG 以优化显示
        const processedSvg = processSvgForDisplay(svg);
        // 生成预览用的 data URL
        const previewUrl = convertSvgToDataUrl(svg);

        setState((prev) => ({
          ...prev,
          renderedSvg: processedSvg,
          error: '',
          isRendering: false,
        }));
        setPreviewImageUrl(previewUrl);
      } catch (err) {
        console.error('Mermaid render error:', err);
        setState((prev) => ({
          ...prev,
          error: 'Invalid mermaid syntax',
          renderedSvg: '',
          isRendering: false,
        }));
        setPreviewImageUrl('');
      }
    },
    [processSvgForDisplay, convertSvgToDataUrl],
  );

  // 切换视图模式
  const toggleViewMode = useCallback(() => {
    const newMode = currentViewMode === 'edit' ? 'preview' : 'edit';
    props.updateAttributes({ mode: newMode });
  }, [currentViewMode, props]);

  // 监听内容变化
  useEffect(() => {
    const currentCode = props.node.textContent || '';

    if (currentCode !== previousCodeRef.current) {
      previousCodeRef.current = currentCode;
      renderMermaid(currentCode);
    }
  }, [props.node.textContent, props.node, renderMermaid]);

  // 初始渲染
  useEffect(() => {
    const initialCode = props.node.textContent || '';
    renderMermaid(initialCode);
  }, [renderMermaid]);

  // 预览图片功能
  const previewImage = useCallback(() => {
    if (previewImageUrl && !state.error && !state.isRendering) {
      setPreviewVisible(true);
    }
  }, [previewImageUrl, state.error, state.isRendering]);

  return (
    <NodeViewWrapper
      className="my-4"
      {...{ [DIFF_CHANGE_ATTR_NAME]: props.node.attrs[DIFF_CHANGE_ATTR_NAME] }}
    >
      <div
        data-drag-handle
        className={cn(
          'group relative',
          props.node.attrs[DIFF_CHANGE_ATTR_NAME] === DIFF_CHANGE_TYPE.REMOVED ? 'opacity-50' : '',
        )}
      >
        <div className="overflow-hidden rounded-lg border border-[#dee0e3] bg-white [.ProseMirror-selectednode_&]:border-[#3b82f6]">
          <div
            className={cn(
              'relative flex',
              currentViewMode === 'preview' ? 'min-h-[300px]' : 'min-h-[200px]',
            )}
          >
            {/* 左侧：代码编辑器 */}
            {currentViewMode === 'edit' && (
              <div className="flex overflow-hidden relative flex-col pt-8 w-1/2 border-r border-gray-300">
                <div className="flex max-h-[600px] flex-1 overflow-auto overscroll-contain">
                  {/* 代码编辑区域 */}
                  <div className="relative flex-1">
                    <pre
                      ref={codeRef}
                      className="m-0 min-h-full w-full resize-none whitespace-pre-wrap break-words border-0 bg-white px-4 pb-2 font-mono text-sm leading-6 text-[#737373] outline-0"
                      style={{
                        fontFamily:
                          'ui-monospace, "Menlo", "Monaco", "Consolas", "Liberation Mono", "Courier New", monospace',
                      }}
                    >
                      <NodeViewContent
                        contenteditable="plaintext-only"
                        as="code"
                        spellCheck={false}
                        className="block p-0 m-0 w-full bg-transparent border-0 outline-0"
                      />
                    </pre>
                  </div>
                </div>
              </div>
            )}

            {/* 右侧：mermaid 渲染视图 */}
            <div
              contentEditable={false}
              className={cn('flex flex-col', currentViewMode === 'edit' ? 'w-1/2' : 'w-full')}
            >
              <div
                id="mermaid-button-container"
                className="flex justify-end items-center px-2 w-full h-8 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
              >
                {/* 视图模式切换按钮 */}
                {shouldShowToggle && (
                  <>
                    <div
                      onClick={toggleViewMode}
                      contentEditable={false}
                      className="flex cursor-pointer items-center gap-1 rounded-sm px-1 py-0.5 text-sm text-gray-600 transition-all duration-200 hover:bg-card-snips hover:text-gray-800"
                    >
                      {currentViewMode === 'edit' ? (
                        <>
                          <Eye size={14} />
                          Preview
                        </>
                      ) : (
                        <>
                          <Edit size={14} />
                          Edit
                        </>
                      )}
                    </div>
                    <div className="mx-2 w-px h-4 bg-divider" />
                  </>
                )}
                <div
                  onClick={previewImage}
                  contentEditable={false}
                  className={cn(
                    'flex cursor-pointer items-center gap-1 rounded-sm p-1 text-sm text-gray-600 transition-all duration-200 hover:bg-card-snips hover:text-gray-800',
                    (!previewImageUrl || state.error || state.isRendering) &&
                      'cursor-not-allowed opacity-50',
                  )}
                >
                  <Maximize2 size={14} />
                </div>
              </div>
              <div className="flex flex-1 justify-center items-center px-2 pb-6">
                {state.isRendering ? (
                  <SimpleLoading className="min-h-[60px]" variant="dot-pulse" />
                ) : state.error ? (
                  <div className="text-center text-red-600">
                    <div className="mb-1 text-sm font-medium">Syntax Error</div>
                    <div className="text-xs">{state.error}</div>
                  </div>
                ) : state.renderedSvg ? (
                  <div
                    onClick={previewImage}
                    // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                    dangerouslySetInnerHTML={{ __html: state.renderedSvg }}
                    className={cn(
                      'cursor-zoom-in overflow-auto',
                      currentViewMode === 'preview'
                        ? 'flex w-full items-center justify-center'
                        : 'max-h-full max-w-full',
                    )}
                  />
                ) : (
                  <div className="text-center text-gray-400">
                    <div className="text-sm">Enter mermaid code to see preview</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Mermaid 图表预览组 */}
        {previewImageUrl && (
          <PreviewGroup
            items={[previewImageUrl]}
            preview={{
              countRender: () => <></>,
              onVisibleChange: (visible) => {
                setPreviewVisible(visible);
              },
              visible: previewVisible,
              current: 0,
            }}
          />
        )}
      </div>
    </NodeViewWrapper>
  );
};
