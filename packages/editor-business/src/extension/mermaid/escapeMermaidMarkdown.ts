/**
 * 转义 mermaid 语法中描述部分可能出现的 markdown 列表语法
 * 只转义双引号内的内容，例如：A["1. Label"] -> A["1\. Label"]
 * @param text 需要转义的 mermaid 代码
 * @returns 转义后的文本
 */
export function escapeMermaidMarkdown(text: string): string {
  return text.replace(/"([^"]*)"/g, (match, content) => {
    const escapedContent = content
      // 转义有序列表：数字后面跟点号和空格
      .replace(/^(\s*)(\d+)\.\s/gm, '$1$2\\. ')
      // 转义无序列表：横线开头
      .replace(/^(\s*)-\s/gm, '$1\\- ')
      // 转义无序列表：星号开头
      .replace(/^(\s*)\*\s/gm, '$1\\* ')
      // 转义无序列表：加号开头
      .replace(/^(\s*)\+\s/gm, '$1\\+ ');

    return `"${escapedContent}"`;
  });
}
