.youmind-editor-node-list-item-ui {
  display: list-item;
  margin: 4px 0;
}

/* 无序列表项样式 */
.youmind-editor-node-bullet-list-ui .youmind-editor-node-list-item-ui {
  list-style: none;
  position: relative;
  padding-left: 1.5em;

  &::before {
    content: "•";
    position: absolute;
    left: 0.35rem;
    top: 0.35rem;
    font-size: 0.8rem;
    line-height: 1;
  }

  /* 确保没有原生的列表标记 */
  &::marker {
    content: none;
  }
}

/* 有序列表项样式 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-list-item-ui {
  list-style-type: decimal;
  list-style-position: outside;

  /* 确保不会有自定义的before伪元素与原生marker冲突 */
  &::before {
    content: none;
  }

  p {
    margin-left: 0.5rem;
    line-height: inherit;
    margin-top: 0;
    margin-bottom: 0;
  }

  &::marker {
    unicode-bidi: isolate;
    font-variant-numeric: tabular-nums;
    text-transform: none;
    text-indent: 0px !important;
    text-align: start !important;
    text-align-last: auto !important;
    font-size: 1em;
    line-height: inherit;
    vertical-align: middle;
  }
}
