.youmind-editor-node-task-item-ui {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;

  p {
    margin: 0;
  }

  label {
    padding-right: 10px;
    display: flex;
    align-items: center;
    padding-top: 0.25rem;

    input[type="checkbox"] {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      width: 16px;
      min-width: 16px;
      height: 16px;
      box-shadow: rgba(31, 34, 37, 0.22) 0px 0px 0px 1.5px inset;
      position: relative;
      vertical-align: middle;
      box-sizing: content-box;
      user-select: none;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      transition:
        background 0.15s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow;
      cursor: pointer;

      &:hover:not(:checked) {
        background: rgba(31, 34, 37, 0.02);
      }

      &:checked {
        background-color: rgb(46, 109, 233);
        box-shadow: rgba(31, 34, 37, 0.12) 0px 0px 0px 1px inset;

        &::after {
          content: "";
          position: absolute;
          top: 1.5px;
          left: 5px;
          width: 6px;
          height: 10px;
          border: solid white;
          border-width: 0 2px 2px 0;
          border-radius: 0 1px 1px 1px;
          transform: rotate(45deg);
        }
      }
    }
  }
}
