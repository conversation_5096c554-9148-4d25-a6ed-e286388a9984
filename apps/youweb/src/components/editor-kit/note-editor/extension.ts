import {
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
} from '@repo/editor-business';

export const getNoteEditorExtension = () => {
  return [
    ...getNodeExtensionList(),
    ...getMarkExtensionList(),
    ...getCommonFunctionExtensionList({
      characterCountOptions: {
        limit: 30000,
      },
      placeholderOptions: {
        placeholder: 'Add a note...',
      },
    }),
  ];
};
