import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { SuggestionKeyDownProps } from '@repo/editor-business';
import { cn } from '@repo/ui/lib/utils';
import { curry } from 'lodash-es';
import { Layers3, Search } from 'lucide-react';
import {
  createRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { mentionTypeList } from '@/hooks/ask-ai/useChatMention';
import type { MentionFileItem } from '../../hooks/ask-ai/useChatMention';
import { CreateAskAIIcon } from './icon/create-ask-ai';
import { CreateSnip } from './icon/create-snip';
import { CreateThought } from './icon/create-thought';
import { MentionFileItemComponent } from './mention-file-item';

export interface MentionFilePanelProps {
  query: string;
  mentionOptions: MentionFileItem[];
  recommendMentionOptions: MentionFileItem[];
  showSearch?: boolean;
  onSelect?: (mentionItem: MentionFileItem) => void;
  rememberLastHover?: boolean;
  className?: string;
}

export interface MentionFilePanelRef {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
  selectItem: (index: number) => void;
}

const filterBySearch = curry((search: string, chatMentionList: MentionFileItem[]) => {
  if (!search) return chatMentionList;

  return chatMentionList.filter((chatMention) => {
    return chatMention.display.toLowerCase().includes(search.toLowerCase());
  });
});

export const MentionFilePanel = forwardRef<MentionFilePanelRef, MentionFilePanelProps>(
  (
    {
      query: propsQuery,
      mentionOptions,
      recommendMentionOptions,
      showSearch = false,
      rememberLastHover = false,
      onSelect,
      className,
    },
    ref,
  ) => {
    const [query, setQuery] = useState(propsQuery);
    const [selectedIndex, setSelectedIndex] = useState(0);

    // Create refs for items and type options
    const itemRefs = useRef<Record<number, React.RefObject<HTMLDivElement>>>({});
    const typeRefs = useRef<Record<number, React.RefObject<HTMLDivElement>>>({});

    useEffect(() => {
      setQuery(propsQuery);
    }, [propsQuery]);

    // When search query changes, reset selection index and update mode
    useEffect(() => {
      if (query) {
        setSelectedIndex(0);
      }
    }, [query]);

    // 没 query 前，会带上 current material、current group、current board；一旦有 query，则不带
    const filteredmentionOptions = useMemo<MentionFileItem[]>(() => {
      const ret = filterBySearch(query)(mentionOptions);
      return query ? ret : recommendMentionOptions;
    }, [recommendMentionOptions, mentionOptions, query]);

    useEffect(() => {
      filteredmentionOptions.forEach((_, index) => {
        if (!itemRefs.current[index]) {
          itemRefs.current[index] = createRef<HTMLDivElement>();
        }
      });
    }, [filteredmentionOptions]);

    useEffect(() => {
      mentionTypeList.forEach((_, index) => {
        if (!typeRefs.current[index]) {
          typeRefs.current[index] = createRef<HTMLDivElement>();
        }
      });
    }, []);

    useEffect(() => {
      if (itemRefs.current[selectedIndex]?.current) {
        itemRefs.current[selectedIndex].current?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }, [selectedIndex]);

    const _mentionTypeListDetailInfo: Record<
      BoardItemTypeEnum | 'board',
      {
        logo: React.ReactNode;
        menuName: string;
        typeName: string;
      }
    > = {
      [BoardItemTypeEnum.SNIP]: {
        logo: <CreateSnip size={16} className="mr-2" />,
        menuName: 'snips',
        typeName: 'snip',
      },
      [BoardItemTypeEnum.THOUGHT]: {
        logo: <CreateThought size={16} className="mr-2" />,
        menuName: 'thoughts',
        typeName: 'thought',
      },
      [BoardItemTypeEnum.CHAT]: {
        logo: <CreateAskAIIcon size={16} className="mr-2" />,
        menuName: 'aiChats',
        typeName: 'aiChat',
      },
      [BoardItemTypeEnum.BOARD_GROUP]: {
        logo: <Layers3 size={16} className="mr-2" />,
        menuName: 'groups',
        typeName: 'group',
      },
      board: {
        logo: <Layers3 size={16} className="mr-2" />,
        menuName: 'board',
        typeName: 'board',
      },
    };

    useImperativeHandle(ref, () => ({
      onKeyDown: (props) => {
        const { event } = props;

        // Handle arrow up/down navigation
        if (event.key === 'ArrowDown') {
          if (filteredmentionOptions.length > 0) {
            setSelectedIndex((selectedIndex + 1) % filteredmentionOptions.length);
            return true;
          }
        }

        if (event.key === 'ArrowUp') {
          if (filteredmentionOptions.length > 0) {
            setSelectedIndex(
              (selectedIndex + filteredmentionOptions.length - 1) % filteredmentionOptions.length,
            );
            return true;
          }
        }

        // Handle selection with Enter or Tab
        if (event.key === 'Enter' || event.key === 'Tab') {
          if (filteredmentionOptions.length > 0) {
            // Select the current item
            const selectedItem = filteredmentionOptions[selectedIndex];
            onSelect?.(selectedItem);
            return true;
          }
        }

        return false;
      },
      selectItem: (index: number) => {
        if (filteredmentionOptions[index]) {
          onSelect?.(filteredmentionOptions[index]);
        }
      },
    }));

    const renderMentionItem = () => {
      return (
        <div className="flex flex-col">
          {showSearch && (
            <div className="flex flex-row items-center gap-2 px-2 py-1 m-2 rounded-lg bg-card-snips">
              <Search size={16} className="text-caption" />
              <input
                placeholder="Search"
                className="w-full text-sm font-normal bg-transparent placeholder:text-caption"
                type="text"
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                }}
              />
            </div>
          )}
          <div>
            {filteredmentionOptions.length ? (
              filteredmentionOptions.map((mention, index) => {
                return (
                  <MentionFileItemComponent
                    key={`${mention.id}-${mention.display}${mention.label ? `-${mention.label}` : ''}`}
                    ref={itemRefs.current[index]}
                    affix={
                      typeof mention.logo === 'string' ? (
                        <img src={mention.logo} alt="logo" className="w-4 h-4" />
                      ) : (
                        mention.logo
                      )
                    }
                    // suffix={mentionTypeListDetailInfo[mention.type].typeName}
                    suffix={mention.description}
                    content={mention.label || mention.display}
                    isSelected={rememberLastHover ? index === selectedIndex : false}
                    onClick={() => {
                      console.log('mention>>>', mention);
                      onSelect?.(mention);
                    }}
                    onMouseEnter={() => {
                      setSelectedIndex(index);
                    }}
                  />
                );
              })
            ) : (
              <div className="p-2 text-sm text-caption">noResults</div>
            )}
          </div>
        </div>
      );
    };

    return (
      <div
        className={cn(
          'flex overflow-y-auto flex-col text-sm max-h-[300px] w-[300px] rounded-[inherit] bg-card text-foreground',
          className,
        )}
      >
        {renderMentionItem()}
      </div>
    );
  },
);

MentionFilePanel.displayName = 'MentionFilePanel';
