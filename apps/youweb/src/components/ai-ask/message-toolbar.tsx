import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { DEFAULT_AI_CHAT_MODEL, type LLMs, MessageModeEnum } from '@repo/common/types/chat/enum';
import { TitleTypeEnum } from '@repo/common/types/thought/types';
import { writeToClipboard } from '@repo/editor-business';
import { htmlToBase64, markdownParse } from '@repo/editor-common';
import { toast } from '@repo/ui/components/ui/sonner';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import hljs from 'highlight.js';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { Copy, Loader2 } from 'lucide-react';
import { Marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import markedKatex from 'marked-katex-extension';
import { useState } from 'react';
import { type AssistantMessage, chatAtom, isMobileChatAtom } from '@/hooks/ask-ai/useChat';
import { panelStateAtom } from '@/hooks/useBoardState';
import { addBoardItemsAndOpenAtom, boardDetailAtom } from '@/hooks/useBoards';
import { thoughtEditorAtom } from '@/hooks/useThought';
import { useTranslation } from '@/hooks/useTranslation';
import { callHTTP } from '@/utils/callHTTP';
import { transformToMarkdown } from '@/utils/chat/transformToMarkdown';
import { InsertToThoughtIcon } from '../icon/insert-to-thought';
import { SaveAsSnipIcon } from '../icon/save-as-snip';
import { LLMTools } from '../llm-tools';
import { Separator } from '../ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { ChatModelList } from './ask-ai-input';
import { RegenerateButton } from './regenerate';

interface MessageToolBarProps {
  messageId: string;
  hideRegenerate?: boolean;
  onRegenerate: (model?: LLMs) => void;
}

const marked = new Marked(
  {
    ...markedHighlight({
      langPrefix: 'hljs language-',
      highlight(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, { language }).value;
      },
    }),
    renderer: {
      code({ text, lang }) {
        return `
        <div class="code-block-container">
          <div class="p-4 pt-0">
          <pre><code class="hljs language-${lang}">${text}</code></pre>
          </div>
        </div>
      `;
      },
      link({ href, title, text }) {
        if (text === 'citation') {
          // 在反存回 thought 的场景下, citation 要剔除
          return '';
        }
        return `
        <a
          href="${href}"
          target="_blank"
          class="ym-markdown-link"
          title="${title || ''}"
        >${text}</a>
      `;
      },
    },
  },
  markedKatex({ nonStandard: true, throwOnError: false }),
);

export function MessageToolbar({
  hideRegenerate = false,
  onRegenerate,
  messageId,
}: MessageToolBarProps) {
  const panelState = useAtomValue(panelStateAtom);
  const thoughtEditor = useAtomValue(thoughtEditorAtom);

  const handleInsert = () => {
    const markdown = transformToMarkdown(message);
    if (thoughtEditor) {
      try {
        thoughtEditor.commands.insertContentByMarkdown(markdown);
      } catch (_err) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      }
    }
    toast('Message content inserted.');
  };

  const { t } = useTranslation();
  const [isSaving, setIsSaving] = useState(false);
  const [chat] = useAtom(chatAtom);
  const message = chat?.messages.find((message) => message.id === messageId) as AssistantMessage;
  const messageIndex = chat?.messages.findIndex((message) => message.id === messageId);
  const lastUserMessage = chat?.messages
    .slice(0, messageIndex)
    .findLast((message) => message.role === 'user');
  const boardDetail = useAtomValue(boardDetailAtom);
  const addBoardItemsAndOpen = useSetAtom(addBoardItemsAndOpenAtom);
  const isMobileChat = useAtomValue(isMobileChatAtom);
  const { trackButtonClick } = useTrackActions();

  const handleRegenerate = (model?: LLMs) => {
    onRegenerate(model);
  };

  const handleSaveToThought = async () => {
    // 上报埋点
    trackButtonClick('ask_ai_save_to_thought_click', {
      model: message.model,
    });

    const markdown = transformToMarkdown(message);

    const html = markdownParse.parse(markdown);
    const base64 = htmlToBase64(html);
    setIsSaving(true);

    const { data, error } = await callHTTP('/api/v1/createThought', {
      method: 'POST',
      body: {
        content: {
          raw: base64,
          plain: markdown,
        },
        board_id: boardDetail?.id,
        title_type: TitleTypeEnum.AI,
        gen_title: true,
      },
    });
    setIsSaving(false);

    if (error) {
      return;
    }

    if (data?.id) {
      const boardItem = {
        ...data.board_item!,
        entity: data,
        entity_type: BoardItemTypeEnum.THOUGHT,
      };

      addBoardItemsAndOpen([boardItem]);

      toast(t('savedAsThought'));
    }
  };

  const handleCopyContent = async () => {
    const markdown = transformToMarkdown(message);

    const html = await marked.parse(markdown);
    writeToClipboard({
      'text/plain': markdown,
      'text/html': html,
    });
    toast(t('successCopy'));
    // 上报埋点
    trackButtonClick('ask_ai_copy_click', {
      model: message.model,
    });
  };

  const currentModel = (message.model as LLMs) || DEFAULT_AI_CHAT_MODEL;

  return (
    <div className="flex flex-row items-center gap-4">
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>
            <Copy
              className="cursor-pointer text-caption hover:text-foreground"
              size={16}
              onClick={handleCopyContent}
            />
          </TooltipTrigger>
          <TooltipContent side="bottom">{t('copy')}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {panelState.panelData?.entity_type === BoardItemTypeEnum.THOUGHT && !isMobileChat && (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger>
              <InsertToThoughtIcon
                size={16}
                className="cursor-pointer text-caption hover:text-foreground"
                onClick={handleInsert}
              />
            </TooltipTrigger>
            <TooltipContent side="bottom">{'Insert'}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>
            {isSaving ? (
              <Loader2 className="w-4 h-4 animate-spin text-caption" />
            ) : (
              <SaveAsSnipIcon
                className="cursor-pointer text-caption hover:text-foreground"
                size={16}
                onClick={handleSaveToThought}
              />
            )}
          </TooltipTrigger>
          <TooltipContent side="bottom">{t('saveAsThought')}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Separator orientation="vertical" className="h-4 bg-muted" />
      {!hideRegenerate && (
        <RegenerateButton
          currentModel={currentModel}
          noModelChoose={lastUserMessage?.mode === MessageModeEnum.AGENT || isMobileChat}
          onRegenerate={() => {
            handleRegenerate();
          }}
        >
          <ChatModelList
            currentModel={currentModel}
            onSelectModel={(model: LLMs) => {
              handleRegenerate(model);
              trackButtonClick('ask_ai_regenerate_model_select', {
                model,
              });
            }}
          />
        </RegenerateButton>
      )}
      <LLMTools traceId={message?.trace_id} />
    </div>
  );
}
