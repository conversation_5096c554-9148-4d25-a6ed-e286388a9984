import type { EditorExtensionOptions, ExtensionInitContent } from '@repo/editor-business';
import {
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
  LocalCollaboration,
  SlashCommand,
} from '@repo/editor-business';
import { DOC_FRAGMENT_FIELED } from '@repo/editor-common';
import { AnyExtension } from '@tiptap/core';
import { Collaboration } from '@tiptap/extension-collaboration';
import { ThoughtAdapter } from '../editor-kit/extensions/thought-adpater-extension';

export type ThoughtEditorExtensionConfg = {
  id: string;
  options?: EditorExtensionOptions;
  initContent: ExtensionInitContent;
};

export const getThoughtEditorExtensions = (config: ThoughtEditorExtensionConfg): AnyExtension[] => {
  const { options, initContent, id } = config;
  const { ydoc } = initContent;
  return [
    ...getCommonFunctionExtensionList(options),
    ...getMarkExtensionList(options),
    ...getNodeExtensionList(options),
    Collaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    ThoughtAdapter.configure({
      id,
    }),
    LocalCollaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    SlashCommand,
  ].filter(Boolean);
};
