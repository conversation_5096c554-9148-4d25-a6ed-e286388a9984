import { isColumnGripSelected, isRowGripSelected, linkPanelPluginKey } from '@repo/editor-business';
import { Bold, Link } from '@repo/editor-common';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { EditorState } from '@tiptap/pm/state';
import type { EditorView } from '@tiptap/pm/view';
import { BubbleMenu, type Editor } from '@tiptap/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { ThoughtAskAIIcon } from '../icon/thought-ask-ai-icon';
import { ThoughtBoldIcon } from '../icon/thought-bold-icon';
import { ThoughtFormatIcon } from '../icon/thought-format-icon';
import { ThoughtLinkIcon } from '../icon/thought-link-icon';
import { FormatContentMenu } from '../toolbar/format-content-menu';
import { SelectionFormatNodeContentMenuConfig } from '../toolbar/format-content-menu-config';
import { calculateModalPosition, showAskAIModal } from './ask-ai-utils';
import { isCustomNodeSelected, isTextSelected } from './utils';

export interface ShouldShowProps {
  editor?: Editor;
  view: EditorView;
  state?: EditorState;
  oldState?: EditorState;
  from?: number;
  to?: number;
}

export const TextMenu = ({ editor }: { editor: Editor }) => {
  const [forceUpdate, setForceUpdate] = useState(0);
  const [isFormatMenuOpen, setIsFormatMenuOpen] = useState(false);
  const [isTextMenuVisible, setIsTextMenuVisible] = useState(true);
  const contentContainerRef = useRef<HTMLDivElement>(null);

  // 监听编辑器关键事件以确保菜单及时更新
  useEffect(() => {
    if (!editor) return;

    // 创建事件处理函数
    const handleUpdate = () => {
      setForceUpdate((prev) => prev + 1);
    };

    // 订阅编辑器事件
    editor.on('selectionUpdate', handleUpdate);
    editor.on('transaction', handleUpdate);
    editor.on('focus', handleUpdate);
    editor.on('blur', handleUpdate);

    // 清理函数
    return () => {
      editor.off('selectionUpdate', handleUpdate);
      editor.off('transaction', handleUpdate);
      editor.off('focus', handleUpdate);
      editor.off('blur', handleUpdate);
    };
  }, [editor]);

  const handleBoldClick = () => {
    // 发送埋点事件
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TEXT_MENU_BOLD_CLICK, {
      action: editor.isActive(Bold.name) ? 'unset' : 'set',
    });

    if (editor.isActive(Bold.name)) {
      editor.chain().focus().unsetBold().run();
      return;
    }
    editor.chain().focus().setBold().run();
  };

  const handleLinkClick = () => {
    // 发送埋点事件
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TEXT_MENU_LINK_CLICK, {
      action: editor.isActive(Link.name) ? 'unset' : 'set',
    });

    if (editor.isActive(Link.name)) {
      editor.chain().focus().unsetLink().run();
      return;
    }

    // 直接弹出面板让用户输入 URL
    const pluginState = linkPanelPluginKey.getState(editor.state);
    if (pluginState) {
      editor.chain().focus().run();
      setTimeout(() => {
        pluginState.linkPanelController.showLinkUrlEditPanel();
      }, 100);
    }
  };

  const shouldShow = useCallback(
    (props: ShouldShowProps) => {
      // 如果 textmenu 被设置为不可见，则不显示
      if (!isTextMenuVisible) {
        return false;
      }

      const { view, from, state } = props;
      if (!state || !from || !view) {
        return false;
      }
      if (isColumnGripSelected(props) || isRowGripSelected(props)) {
        return false;
      }
      const selection = state.selection;

      if (selection.empty) {
        return false;
      }

      const domAtPos = view.domAtPos(from || 0).node;
      const nodeDOM = view.nodeDOM(from || 0);
      const node = nodeDOM || domAtPos;

      if (!node) {
        return false;
      }
      if (isCustomNodeSelected(editor, node as HTMLElement)) {
        return false;
      }

      return isTextSelected({ editor });
    },
    [editor, forceUpdate, isTextMenuVisible], // 添加 isTextMenuVisible 依赖
  );

  // 检查加粗和链接的 active 状态
  const isBoldActive = editor.isActive(Bold.name);
  const isLinkActive = editor.isActive(Link.name);

  const handleAskAIClick = () => {
    // 发送埋点事件
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TEXT_MENU_ASK_AI_CLICK);

    if (!contentContainerRef.current) return;

    editor.chain().focus().run();

    // 调用计算位置的逻辑
    const position = calculateModalPosition(editor, contentContainerRef.current);

    // 将 textmenu 隐藏
    setIsTextMenuVisible(false);

    // 显示 modal
    showAskAIModal(position, editor);
  };

  return (
    <BubbleMenu
      editor={editor}
      className="flex items-center h-8 px-2 border rounded-full shadow-md border-muted bg-card"
      tippyOptions={{
        popperOptions: { placement: 'top-start' },
        duration: 100,
      }}
      shouldShow={shouldShow}
      updateDelay={50}
    >
      <div ref={contentContainerRef} className="flex items-center w-full h-full">
        <div
          className="flex items-center justify-center h-6 gap-2 p-1 rounded-md cursor-pointer hover:bg-card-snips"
          onClick={handleAskAIClick}
        >
          <ThoughtAskAIIcon size={16} className="text-[#A25AD9]" />
          <div className="ml-0.5 text-sm">Ask AI</div>
        </div>
        <div className="mx-2 h-4 w-[1px] bg-muted" />
        <div className="flex gap-x-1">
          <div className="flex items-center justify-center w-6 h-6 rounded-md hover:bg-card-snips">
            <TooltipProvider delayDuration={500}>
              <Tooltip open={!isFormatMenuOpen ? undefined : false}>
                <TooltipTrigger>
                  <FormatContentMenu
                    editor={editor}
                    onOpenChange={setIsFormatMenuOpen}
                    contentContainer={contentContainerRef.current}
                    menuConfig={SelectionFormatNodeContentMenuConfig}
                    sideOffset={10}
                    side="bottom"
                    contentClassName="max-h-[16rem]"
                    scene="text_menu"
                  >
                    <div>
                      <ThoughtFormatIcon size={16} className="cursor-pointer text-foreground" />
                    </div>
                  </FormatContentMenu>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Format</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div
            className={`flex h-6 w-6 items-center justify-center rounded-md ${isBoldActive ? 'bg-card-snips' : 'hover:bg-card-snips'}`}
          >
            <TooltipProvider delayDuration={500}>
              <Tooltip open={!isFormatMenuOpen ? undefined : false}>
                <TooltipTrigger>
                  <div onClick={handleBoldClick}>
                    <ThoughtBoldIcon size={16} />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Bold</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div
            className={`flex h-6 w-6 items-center justify-center rounded-md ${isLinkActive ? 'bg-card-snips' : 'hover:bg-card-snips'}`}
            onClick={handleLinkClick}
          >
            <TooltipProvider delayDuration={500}>
              <Tooltip open={!isFormatMenuOpen ? undefined : false}>
                <TooltipTrigger>
                  <div>
                    <ThoughtLinkIcon size={16} />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Link</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </BubbleMenu>
  );
};
