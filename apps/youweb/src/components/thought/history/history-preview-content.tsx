import type { Thought, ThoughtVersion } from '@repo/common/types/thought/types';
import { ThoughtPreview } from '@repo/editor-business';
import { useRef } from 'react';

interface HistoryPreviewContentProps {
  thought: ThoughtVersion;
}

export const HistoryPreviewContent = (props: HistoryPreviewContentProps) => {
  const { thought } = props;
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={containerRef} className="h-auto w-[41.5rem]">
      <ThoughtPreview
        key={thought.id}
        id={thought.id}
        thought={
          {
            ...thought,
            title: thought.thought_title,
            id: thought.id,
          } as unknown as Thought
        }
        clearDiff={false}
        showNavigation={false}
        extensionsOptions={{
          imageOptions: { maxviewEnable: false },
        }}
      />
    </div>
  );
};
