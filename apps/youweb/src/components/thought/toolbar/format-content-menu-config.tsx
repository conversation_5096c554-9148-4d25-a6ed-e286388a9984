import { isCurrentNodeEmpty, Paragraph } from '@repo/editor-business';
import type { Editor } from '@tiptap/react';
import {
  Code,
  Heading1,
  Heading2,
  Heading3,
  ItalicIcon,
  List,
  ListOrdered,
  ListTodo,
  StrikethroughIcon,
  Type,
  UnderlineIcon,
} from 'lucide-react';
import type { ReactNode } from 'react';
import { ThoughtQuoteIcon } from '../icon/thought-quote-icon';

export interface FormatContentMenuItem {
  icon: ReactNode;
  label: string;
  onClick: (editor: Editor) => void;
  markdownTip?: string;
}

export const FormatNodeContentMenuConfig: FormatContentMenuItem[] = [
  {
    icon: <Heading1 size={16} />,
    label: 'Title',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 1 }).scrollCursorIntoView().run();
    },
    markdownTip: '#',
  },
  {
    icon: <Heading2 size={16} />,
    label: 'Subtitle',
    markdownTip: '##',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 2 }).scrollCursorIntoView().run();
    },
  },
  {
    icon: <Heading3 size={16} />,
    label: 'Heading',
    markdownTip: '###',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 3 }).scrollCursorIntoView().run();
    },
  },
  {
    icon: <Type size={14} />,
    label: 'Text',
    onClick: (editor) => {
      editor.chain().focus().setParagraph().scrollCursorIntoView().run();
    },
  },
  {
    icon: <ListTodo size={16} />,
    label: 'Task list',
    onClick: (editor) => {
      editor.chain().focus().toggleTaskList().scrollCursorIntoView().run();
    },
    markdownTip: '[]',
  },
  {
    icon: <ListOrdered size={16} />,
    label: 'Numbered list',
    onClick: (editor) => {
      editor.chain().focus().toggleOrderedList().scrollCursorIntoView().run();
    },
    markdownTip: '1.',
  },
  {
    icon: <List size={16} />,
    label: 'Bullet list',
    onClick: (editor) => {
      editor.chain().focus().toggleBulletList().scrollCursorIntoView().run();
    },
    markdownTip: '-',
  },
  {
    icon: <ThoughtQuoteIcon size={16} />,
    label: 'Quote',
    markdownTip: '>',
    onClick: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setBlockquote()
        .scrollCursorIntoView()
        .run();
    },
  },
  {
    icon: <Code size={16} />,
    label: 'Code',
    markdownTip: '```',
    onClick: (editor) => {
      if (isCurrentNodeEmpty(editor)) {
        editor.chain().focus().setCodeBlock().scrollCursorIntoView().run();
        return;
      }

      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setCodeBlock()
        .scrollCursorIntoView()
        .run();
    },
  },
];

export const SelectionFormatNodeContentMenuConfig: FormatContentMenuItem[] = [
  {
    icon: <ItalicIcon size={16} />,
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleItalic().run();
    },
    label: 'Italic',
  },
  {
    icon: <UnderlineIcon size={16} />,
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleUnderline().run();
    },
    label: 'Underline',
  },
  {
    icon: <StrikethroughIcon size={16} />,
    onClick: (editor: Editor) => {
      editor.chain().focus().toggleStrike().run();
    },
    label: 'Strikethrough',
  },
  {
    icon: <Code size={16} />,
    label: 'Code',
    markdownTip: '`',
    onClick: (editor) => {
      editor.chain().focus().toggleCode().run();
    },
  },
  {
    icon: <Heading1 size={16} />,
    label: 'Title',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 1 }).scrollCursorIntoView().run();
    },
    markdownTip: '#',
  },
  {
    icon: <Heading2 size={16} />,
    label: 'Subtitle',
    markdownTip: '##',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 2 }).scrollCursorIntoView().run();
    },
  },
  {
    icon: <Heading3 size={16} />,
    label: 'Heading',
    markdownTip: '###',
    onClick: (editor) => {
      editor.chain().focus().setHeading({ level: 3 }).scrollCursorIntoView().run();
    },
  },
  {
    icon: <Type size={14} />,
    label: 'Text',
    onClick: (editor) => {
      editor.chain().focus().setParagraph().scrollCursorIntoView().run();
    },
  },
  {
    icon: <ListTodo size={16} />,
    label: 'Task list',
    onClick: (editor) => {
      editor.chain().focus().toggleTaskList().scrollCursorIntoView().run();
    },
    markdownTip: '[]',
  },
  {
    icon: <ListOrdered size={16} />,
    label: 'Numbered list',
    onClick: (editor) => {
      editor.chain().focus().toggleOrderedList().scrollCursorIntoView().run();
    },
    markdownTip: '1.',
  },
  {
    icon: <List size={16} />,
    label: 'Bullet list',
    onClick: (editor) => {
      editor.chain().focus().toggleBulletList().scrollCursorIntoView().run();
    },
    markdownTip: '-',
  },
  {
    icon: <ThoughtQuoteIcon size={16} />,
    label: 'Quote',
    markdownTip: '>',
    onClick: (editor) => {
      editor
        .chain()
        .focus()
        .insertContentAt(editor.state.selection.to, {
          type: Paragraph.name,
        })
        .setBlockquote()
        .scrollCursorIntoView()
        .run();
    },
  },
];
