import './thought-body.less';
import type { Thought } from '@repo/common/types/thought/types';
import { TitleTypeEnum } from '@repo/common/types/thought/types';
import {
  type ExtensionInitContent,
  TableColMenu,
  TableRowMenu,
  ThoughtEditor,
  ThoughtEditorProps,
  ThoughtEditorReadyParams,
} from '@repo/editor-business';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import type { Editor } from '@tiptap/core';
import { throttle } from 'lodash-es';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { EDITOR_EKY_MAP, isNavigationOrFunctionKey } from './const';
import { getThoughtEditorExtensions } from './editor-options';
import { ThoughtGenerateTitleIcon } from './icon/thought-generate-title';
import { TextMenu } from './text-menu';
import type {
  OnSelectionChangeParams,
  ThoughtBodyComponentProps,
  ThoughtBodyComponentRef,
} from './type';

const MemoizedThoughtEditor = memo<ThoughtEditorProps>((props) => {
  return <ThoughtEditor {...props} />;
});

MemoizedThoughtEditor.displayName = 'MemoizedThoughtEditor';

export const ThoughtBodyComponent = forwardRef<ThoughtBodyComponentRef, ThoughtBodyComponentProps>(
  (props, ref) => {
    const { thought, onUpdate = () => {}, onSelectionChange, workflow, id, onReady } = props;

    const [isGenTitle, setIsGenTitle] = useState(false);
    const [editor, setEditor] = useState<Editor | null>(null);
    const [title, setTitle] = useState(thought.title);
    const [titleType, setTitleType] = useState(thought.title_type);

    const titleELRef = useRef<HTMLDivElement>(null);
    const titleValueRef = useRef<string>(thought.title);
    const titleTypeValueRef = useRef<TitleTypeEnum>(thought.title_type);
    const isGenTitleRef = useRef<boolean>(isGenTitle);
    const onUpdateRef = useRef<ThoughtBodyComponentRef['onUpdate']>(onUpdate);
    const onSelectionChangeRef =
      useRef<ThoughtBodyComponentRef['onSelectionChange']>(onSelectionChange);
    const thoughtRef = useRef<Thought>(thought);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      titleValueRef.current = title;
      titleTypeValueRef.current = titleType;
      isGenTitleRef.current = isGenTitle;
      onUpdateRef.current = onUpdate;
      onSelectionChangeRef.current = onSelectionChange;
      thoughtRef.current = thought;
    }, [title, isGenTitle, titleType, onUpdate, onSelectionChange, thought]);

    // 创建节流的 editTitle 函数，300ms 执行一次
    const throttledEditTitle = useMemo(
      () =>
        throttle((text: string) => {
          workflow?.editTitle(text);
        }, 300),
      [workflow],
    );

    // 创建节流的 onUpdate 函数，100ms 执行一次
    const throttledOnUpdate = useMemo(
      () =>
        throttle((thoughtData: Partial<Thought>) => {
          onUpdateRef.current(thoughtData);
        }, 100),
      [],
    );

    // 组件卸载时取消节流
    useEffect(() => {
      return () => {
        throttledEditTitle.cancel();
        throttledOnUpdate.cancel();
      };
    }, [throttledEditTitle, throttledOnUpdate]);

    // 初始化标题内容
    useEffect(() => {
      if (titleELRef.current) {
        const shouldShowTitle = titleType !== TitleTypeEnum.DEFAULT;
        const initialText = shouldShowTitle ? title : '';
        if (titleELRef.current.innerText !== initialText) {
          titleELRef.current.innerText = initialText;
        }
      }
    }, []); // 只在组件挂载时执行一次

    // 处理标题显示逻辑：当 titleType 为 DEFAULT 时，不显示内容
    useEffect(() => {
      if (titleELRef.current && !titleELRef.current.matches(':focus')) {
        // 只在元素没有焦点时更新，避免干扰用户输入
        const shouldShowTitle = titleType !== TitleTypeEnum.DEFAULT;
        if (titleELRef.current.innerText !== (shouldShowTitle ? title : '')) {
          titleELRef.current.innerText = shouldShowTitle ? title : '';
        }
      }
    }, [titleType]); // 只在 titleType 改变时更新，不监听 title 变化

    useImperativeHandle(ref, () => ({
      onUpdate: (data: Partial<Thought>) => {
        onUpdateRef.current({ ...thoughtRef.current, ...data });
      },
      getTitle: () => titleELRef.current?.innerText || '',
      setTitle: (newTitle: string) => {
        setTitle(newTitle);
        titleValueRef.current = newTitle;
        if (titleELRef.current && !titleELRef.current.matches(':focus')) {
          // 只在元素没有焦点时更新DOM，避免干扰用户输入
          const shouldShowTitle = titleTypeValueRef.current !== TitleTypeEnum.DEFAULT;
          titleELRef.current.innerText = shouldShowTitle ? newTitle : '';
        }
      },
      getIsGenTitle: () => isGenTitleRef.current,
      setIsGenTitle: (isGenTitle: boolean) => {
        setIsGenTitle(isGenTitle);
      },
      getTitleType: () => titleTypeValueRef.current,
      setTitleType: (titleType: TitleTypeEnum) => {
        setTitleType(titleType);
      },
      onSelectionChange: (selection: OnSelectionChangeParams) => {
        onSelectionChangeRef.current?.(selection);
      },
    }));

    const handleTitleKeydown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.nativeEvent.isComposing || event.keyCode === 229) {
        return;
      }
      if (event.key === EDITOR_EKY_MAP.Enter) {
        event.preventDefault();
        editor?.commands.focus('start', {
          scrollIntoView: false,
        });
        return;
      }
      // 禁止在输入框中按 command + s 保存
      if (event.key === 's' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault();
        return;
      }

      // 忽略方向键、功能键等非输入按键
      if (isNavigationOrFunctionKey(event.key)) {
        return;
      }

      // 只有在实际输入内容时才更新
      const text = event.currentTarget.innerText;
      // 使用节流函数调用 onUpdate 更新标题
      throttledOnUpdate({ ...thoughtRef.current, title: text.trim() });
      // 使用节流函数通知workflow
      throttledEditTitle(text);
    };

    const handleTitleInput = (event: React.FormEvent<HTMLDivElement>) => {
      const target = event.currentTarget;
      const text = target.innerText;

      // 如果文本为空，确保元素真正为空以显示placeholder
      if (!text || !text.trim()) {
        // 清空所有内容，包括 br 标签
        while (target.firstChild) {
          target.removeChild(target.firstChild);
        }
      }

      // 更新本地状态
      setTitle(text);
      titleValueRef.current = text;

      // 使用节流函数调用 onUpdate 更新标题
      throttledOnUpdate({ ...thoughtRef.current, title: text.trim() });

      // 使用节流函数通知workflow，避免快速输入时的卡顿
      throttledEditTitle(text.trim());
    };

    const onEditorReady = useCallback(
      (params: ThoughtEditorReadyParams) => {
        const { editor } = params;
        setEditor(editor);
        onReady?.(params);
      },
      [onReady],
    );

    const handleGenTitle = useCallback(() => {
      if (isGenTitle || !workflow) {
        return;
      }
      workflow.manualUpdateAITitle();
    }, [workflow, isGenTitle]);

    return (
      <div
        className="text-foreground flex h-full min-h-[70vh] flex-col pb-[50vh]"
        ref={containerRef}
        onClick={(e) => {
          // 只有当点击的是容器本身，而不是编辑器内容时，才执行兜底逻辑
          if (e.target === e.currentTarget) {
            e.stopPropagation();
            editor?.commands.focus('end', {
              scrollIntoView: false,
            });
          }
        }}
      >
        <div
          id="thought-title-container"
          className="relative items-start justify-between w-full mb-4 group"
        >
          {editor?.isEditable && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className="absolute left-[-28px] pt-1 cursor-pointer opacity-0 hover:!opacity-100 group-hover:opacity-80 before:content-[''] before:absolute before:top-[-20px] before:left-[-20px] before:right-[-5px] before:bottom-[-20px] transition-all duration-300"
                    onClick={handleGenTitle}
                  >
                    <ThoughtGenerateTitleIcon size={20} />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Generate title</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <div
            id={'thought-title'}
            data-placeholder={isGenTitle ? 'Generating title...' : 'New thought'}
            className="p-0 cursor-text w-full h-auto text-2xl bg-transparent border-none outline-none font-[600] min-h-10 text-[rgba(0,0,0,0.88)] placeholder:text-[rgba(0,0,0,0.24)] empty:before:text-[rgba(0,0,0,0.24)] empty:before:content-[attr(data-placeholder)]"
            ref={titleELRef}
            onKeyDown={handleTitleKeydown}
            onInput={handleTitleInput}
            contentEditable
          />
        </div>
        <MemoizedThoughtEditor
          id={id}
          content={thought.content}
          onReady={onEditorReady}
          className="thought-body-editor"
          extensions={(context: ExtensionInitContent) =>
            getThoughtEditorExtensions({
              id,
              initContent: context,
            })
          }
        />
        {editor && <TextMenu editor={editor} />}
        {editor && <TableRowMenu editor={editor} appendTo={containerRef} />}
        {editor && <TableColMenu editor={editor} appendTo={containerRef} />}
      </div>
    );
  },
);
