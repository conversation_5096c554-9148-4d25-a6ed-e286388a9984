{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "jsx": "react-jsx",
    "target": "ES2020",
    "noEmit": true,
    "skipLibCheck": true,
    "useDefineForClassFields": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,

    /* modules */
    "module": "ESNext",
    "resolveJsonModule": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,

    /* path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@repo/ui/*": ["../../packages/ui/src/*"],
      "@components/*": ["src/components/*"],
      "@repo/common/*": ["../../packages/common/src/*"],
      "@repo/editor-common/*": ["../../packages/editor-common/src/*"],
      "@repo/editor-business": ["../../packages/editor-business/src/index"],
      "@repo/di": ["../../packages/di/src/index"],
      "@repo/config": ["../../packages/config/src/index"]
    },
    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  },
  "include": [
    "src",
    "src/typings",
    "../../packages/editor-ui/src/common/function.ts",
    "../../packages/editor-ui/src/common/mark.ts",
    "../../packages/editor-ui/src/common/node.ts"
  ]
}
